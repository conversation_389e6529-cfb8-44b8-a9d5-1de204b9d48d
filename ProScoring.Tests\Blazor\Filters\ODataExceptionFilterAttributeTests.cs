using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.OData.Extensions;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.OData;
using NSubstitute;
using ProScoring.Blazor.Filters;
using Xunit;

namespace ProScoring.Tests.Blazor.Filters;

/// <summary>
/// Tests for the <see cref="ODataExceptionFilterAttribute"/> class which handles OData-specific exceptions
/// and converts them to appropriate HTTP responses.
/// </summary>
public class ODataExceptionFilterAttributeTests
{
    #region Fields
    private readonly ILogger<ODataExceptionFilterAttribute> _mockLogger;
    private readonly ODataExceptionFilterAttribute _filter;
    #endregion Fields

    #region Constructors
    public ODataExceptionFilterAttributeTests()
    {
        _mockLogger = Substitute.For<ILogger<ODataExceptionFilterAttribute>>();
        _filter = new ODataExceptionFilterAttribute(_mockLogger);
    }
    #endregion Constructors

    #region Test Methods
    [Fact]
    public void OnException_WithODataException_ReturnsBadRequest()
    {
        // Arrange
        var exception = new ODataException("Invalid $filter expression");
        var context = CreateExceptionContext(exception);

        // Act
        _filter.OnException(context);

        // Assert
        context.ExceptionHandled.Should().BeTrue();
        context.Result.Should().BeOfType<BadRequestObjectResult>();

        var result = context.Result as BadRequestObjectResult;
        result.Should().NotBeNull();

        var badRequestResult = result!;
        badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);

        var errorObj = badRequestResult.Value;
        errorObj.Should().NotBeNull();

        dynamic dynamicError = errorObj!;
        ((object)dynamicError.error).Should().NotBeNull();
        ((string)dynamicError.error.code).Should().Be("ODataQueryError");
        ((string)dynamicError.error.message).Should().Be("Invalid $filter expression");
    }

    [Fact]
    public void OnException_WithInvalidOperationExceptionInODataContext_ReturnsBadRequest()
    {
        // Arrange
        var exception = new InvalidOperationException("Invalid operation in OData context");
        var context = CreateExceptionContext(exception, isODataRequest: true);

        // Act
        _filter.OnException(context);

        // Assert
        context.ExceptionHandled.Should().BeTrue();
        context.Result.Should().BeOfType<BadRequestObjectResult>();

        var result = context.Result as BadRequestObjectResult;
        result.Should().NotBeNull();

        var badRequestResult = result!;
        badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);

        var errorObj = badRequestResult.Value;
        errorObj.Should().NotBeNull();

        dynamic dynamicError = errorObj!;
        ((object)dynamicError.error).Should().NotBeNull();
        ((string)dynamicError.error.code).Should().Be("InvalidODataOperation");
        ((string)dynamicError.error.message).Should().Be("An error occurred while processing the OData request");
    }

    [Fact]
    public void OnException_WithNonODataException_DoesNotHandleException()
    {
        // Arrange
        var exception = new ArgumentException("Some non-OData exception");
        var context = CreateExceptionContext(exception);

        // Act
        _filter.OnException(context);

        // Assert
        context.ExceptionHandled.Should().BeFalse();
        context.Result.Should().BeNull();
    }
    #endregion Test Methods

    #region Helper Methods
    /// <summary>
    /// Creates an ExceptionContext for testing exception handling.
    /// </summary>
    /// <param name="exception">The exception to include in the context.</param>
    /// <param name="isODataRequest">Whether to mark the request as an OData request.</param>
    /// <returns>A configured ExceptionContext for testing.</returns>
    private static ExceptionContext CreateExceptionContext(Exception exception, bool isODataRequest = false)
    {
        var httpContext = new DefaultHttpContext();
        httpContext.Request.QueryString = new QueryString("?$filter=Name eq 'Test'");

        if (isODataRequest)
        {
            httpContext.Items["Microsoft.AspNetCore.OData.Extensions.ODataFeature"] = new object();
        }

        var actionContext = new ActionContext(httpContext, new RouteData(), new ActionDescriptor());

        return new ExceptionContext(actionContext, new List<IFilterMetadata>()) { Exception = exception };
    }
    #endregion Helper Methods
}
