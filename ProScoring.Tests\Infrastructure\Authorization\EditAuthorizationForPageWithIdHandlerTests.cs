using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using NSubstitute;
using ProScoring.Infrastructure.Authorization;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Authorization;

/// <summary>
/// Tests for the EditAuthorizationForPageWithIdHandler class.
/// Verifies the authorization behavior for editing pages using ID-based authorization.
/// Tests both HttpContext and NavigationManager based page ID retrieval.
/// </summary>
public class EditAuthorizationForPageWithIdHandlerTests
{
    private readonly EditAuthorizationForPageWithIdHandler _handler;
    private readonly IAuthorizationProvider _mockAuthProvider;
    private FakeNavigationManager _navManager;
    private readonly EditAuthorizationForPageWithIdHandler.Requirement _requirement;

    public EditAuthorizationForPageWithIdHandlerTests()
    {
        _mockAuthProvider = Substitute.For<IAuthorizationProvider>();
        _navManager = new FakeNavigationManager("http://localhost/", "http://localhost/");
        _handler = new EditAuthorizationForPageWithIdHandler(_mockAuthProvider, _navManager);
        _requirement = new EditAuthorizationForPageWithIdHandler.Requirement();
    }

    private ClaimsPrincipal CreateUser(bool isAuthenticated, IEnumerable<Claim>? claims = null)
    {
        var identity = new ClaimsIdentity(claims, isAuthenticated ? "TestAuthType" : null);
        return new ClaimsPrincipal(identity);
    }

    private AuthorizationHandlerContext CreateContext(ClaimsPrincipal user, object? resource = null)
    {
        return new AuthorizationHandlerContext(new[] { _requirement }, user, resource);
    }

    private DefaultHttpContext CreateMockHttpContextWithPageId(StringValues pageId)
    {
        var httpContext = new DefaultHttpContext();
        httpContext.Request.QueryString = new QueryString($"?id={pageId}");
        return httpContext;
    }

    /// <summary>
    /// Ensures that unauthenticated users do not pass the requirement.
    /// </summary>
    [Fact]
    public async Task HandleRequirementAsync_UserNotAuthenticated_DoesNotSucceed()
    {
        // Arrange
        var user = CreateUser(false);
        var mockHttpContext = Substitute.For<HttpContext>();
        var context = CreateContext(user, mockHttpContext);

        // Act
        await _handler.HandleAsync(context);

        // Assert
        context.HasSucceeded.Should().BeFalse();
        await _mockAuthProvider
            .DidNotReceive()
            .IsAuthorizedAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<string>(), Arg.Any<string>());
    }

    /// <summary>
    /// Ensures that the requirement succeeds when the provider returns true for a page ID from HttpContext.
    /// </summary>
    [Fact]
    public async Task HandleRequirementAsync_PageIdFromHttpContext_ProviderReturnsTrue_Succeeds()
    {
        // Arrange
        var user = CreateUser(true);
        var pageId = "page123";
        var mockHttpContext = CreateMockHttpContextWithPageId(pageId);
        var context = CreateContext(user, mockHttpContext);

        _mockAuthProvider.IsAuthorizedAsync(user, pageId, AuthTypes.Actions.EDIT).Returns(Task.FromResult(true));

        // Act
        await _handler.HandleAsync(context);

        // Assert
        context.HasSucceeded.Should().BeTrue();
        await _mockAuthProvider.Received(1).IsAuthorizedAsync(user, pageId, AuthTypes.Actions.EDIT);
    }

    /// <summary>
    /// Ensures that the requirement does not succeed when the provider returns false for a page ID from HttpContext.
    /// </summary>
    [Fact]
    public async Task HandleRequirementAsync_PageIdFromHttpContext_ProviderReturnsFalse_DoesNotSucceed()
    {
        // Arrange
        var user = CreateUser(true);
        var pageId = "page456";
        var mockHttpContext = CreateMockHttpContextWithPageId(pageId);
        var context = CreateContext(user, mockHttpContext);

        _mockAuthProvider.IsAuthorizedAsync(user, pageId, AuthTypes.Actions.EDIT).Returns(Task.FromResult(false));

        // Act
        await _handler.HandleAsync(context);

        // Assert
        context.HasSucceeded.Should().BeFalse();
        await _mockAuthProvider.Received(1).IsAuthorizedAsync(user, pageId, AuthTypes.Actions.EDIT);
    }

    /// <summary>
    /// Ensures that an empty page ID from HttpContext does not pass the requirement.
    /// </summary>
    [Fact]
    public async Task HandleRequirementAsync_EmptyPageIdFromHttpContext_DoesNotSucceed()
    {
        // Arrange
        var user = CreateUser(true);
        var mockHttpContext = CreateMockHttpContextWithPageId(StringValues.Empty);
        var context = CreateContext(user, mockHttpContext);

        // Act
        await _handler.HandleAsync(context);

        // Assert
        context.HasSucceeded.Should().BeFalse();
        await _mockAuthProvider
            .DidNotReceive()
            .IsAuthorizedAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<string>(), Arg.Any<string>());
    }

    /// <summary>
    /// Ensures that an invalid URI in NavigationManager does not pass the requirement.
    /// </summary>
    [Fact]
    public async Task HandleRequirementAsync_InvalidNavManagerUri_DoesNotSucceedAndHandlesError()
    {
        // Arrange
        var user = CreateUser(true);
        _navManager = new FakeNavigationManager("http://localhost/", "http://localhost/InvalidPageWithoutQuery");
        var handler = new EditAuthorizationForPageWithIdHandler(_mockAuthProvider, _navManager);
        var context = CreateContext(user, null);

        // Act
        await _handler.HandleAsync(context);

        // Assert
        context.HasSucceeded.Should().BeFalse();
        await _mockAuthProvider
            .DidNotReceive()
            .IsAuthorizedAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<string>(), Arg.Any<string>());
    }

    /// <summary>
    /// Ensures that no page ID available in HttpContext does not pass the requirement.
    /// </summary>
    [Fact]
    public async Task HandleRequirementAsync_NoPageIdAvailable_HttpContextNoQuery_DoesNotSucceed()
    {
        // Arrange
        var user = CreateUser(true);
        var mockHttpContext = Substitute.For<HttpContext>();
        var mockHttpRequest = Substitute.For<HttpRequest>();
        mockHttpRequest.Query.Returns(Substitute.For<IQueryCollection>());
        mockHttpContext.Request.Returns(mockHttpRequest);
        var context = CreateContext(user, mockHttpContext);

        // Act
        await _handler.HandleAsync(context);

        // Assert
        context.HasSucceeded.Should().BeFalse();
        await _mockAuthProvider
            .DidNotReceive()
            .IsAuthorizedAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<string>(), Arg.Any<string>());
    }

    /// <summary>
    /// Ensures that no page ID available in NavigationManager does not pass the requirement.
    /// </summary>
    [Fact]
    public async Task HandleRequirementAsync_NoPageIdAvailable_NavManagerNoQuery_DoesNotSucceed()
    {
        // Arrange
        var user = CreateUser(true);
        _navManager = new FakeNavigationManager("http://localhost/", "http://localhost/PageWithoutIdQuery");
        var handler = new EditAuthorizationForPageWithIdHandler(_mockAuthProvider, _navManager);
        var context = CreateContext(user, null);

        // Act
        await _handler.HandleAsync(context);

        // Assert
        context.HasSucceeded.Should().BeFalse();
        await _mockAuthProvider
            .DidNotReceive()
            .IsAuthorizedAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<string>(), Arg.Any<string>());
    }
}
