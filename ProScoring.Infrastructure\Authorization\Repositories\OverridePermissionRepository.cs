using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.Authorization.Repositories.Interfaces;
using ProScoring.Infrastructure.Database;

namespace ProScoring.Infrastructure.Authorization.Repositories
{
    /// <summary>
    /// Implements the repository for managing OverridePermission entities.
    /// </summary>
    /// <param name="context">The application database context.</param>
    /// <param name="logger">The logger.</param>
    public class OverridePermissionRepository(
        IApplicationDbContext context,
        ILogger<OverridePermissionRepository> logger
    ) : IOverridePermissionRepository
    {
        private readonly IApplicationDbContext _context = context;
        private readonly ILogger<OverridePermissionRepository> _logger = logger;

        /// <inheritdoc/>
        public async Task<OverridePermission?> GetByIdAsync(string id)
        {
            _logger.LogTrace("Getting OverridePermission by Id={Id}", id);
            return await _context.OverridePermissions.FindAsync(id);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<OverridePermission>> GetByUserAsync(string userId)
        {
            _logger.LogTrace("Getting OverridePermissions by UserId={UserId}", userId);
            return await _context.OverridePermissions.Where(op => op.UserId == userId).ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<OverridePermission>> GetByUserAndTargetAsync(string userId, string targetId)
        {
            _logger.LogTrace(
                "Getting OverridePermissions by UserId={UserId} and TargetId={TargetId}",
                userId,
                targetId
            );
            return await _context
                .OverridePermissions.Where(op => op.UserId == userId && op.TargetId == targetId)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<OverridePermission>> GetByUserTargetAndActionAsync(
            string userId,
            string targetId,
            string actionName
        )
        {
            _logger.LogTrace(
                "Getting OverridePermissions by UserId={UserId}, TargetId={TargetId}, ActionName={ActionName}",
                userId,
                targetId,
                actionName
            );
            return await _context
                .OverridePermissions.Where(op =>
                    op.UserId == userId && op.TargetId == targetId && op.ActionName == actionName
                )
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<OverridePermission>> GetActiveByUserTargetAndActionAsync(
            string userId,
            string targetId,
            string actionName,
            DateTimeOffset currentTime
        )
        {
            _logger.LogTrace(
                "Getting active OverridePermissions by UserId={UserId}, TargetId={TargetId}, ActionName={ActionName} at {CurrentTime}",
                userId,
                targetId,
                actionName,
                currentTime
            );
            var results = await _context
                .OverridePermissions.Where(op =>
                    op.UserId == userId && op.TargetId == targetId && op.ActionName == actionName
                )
                .ToListAsync();

            // Filter by expiration date in memory since EF Core has trouble translating nullable DateTime comparisons
            return results.Where(op => op.ExpiresAt == null || op.ExpiresAt > currentTime).ToList();
        }

        /// <inheritdoc/>
        public async Task AddAsync(OverridePermission overridePermission)
        {
            _logger.LogTrace(
                "Adding new OverridePermission for UserId={UserId}, TargetId={TargetId}, ActionName={ActionName}",
                overridePermission.UserId,
                overridePermission.TargetId,
                overridePermission.ActionName
            );
            await _context.OverridePermissions.AddAsync(overridePermission);
            // SaveChangesAsync to be called by Unit of Work or service layer.
        }

        /// <inheritdoc/>
        public Task UpdateAsync(OverridePermission overridePermission)
        {
            _logger.LogTrace("Updating OverridePermission Id={Id}", overridePermission.Id);
            _context.OverridePermissions.Update(overridePermission);
            // SaveChangesAsync to be called by Unit of Work or service layer.
            return Task.CompletedTask; // Update is synchronous for EF Core until SaveChanges is called.
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteByIdAsync(string id)
        {
            _logger.LogTrace("Deleting OverridePermission by Id={Id}", id);
            var entity = await _context.OverridePermissions.FindAsync(id);
            if (entity != null)
            {
                _context.OverridePermissions.Remove(entity);
                // SaveChangesAsync to be called by Unit of Work or service layer.
                return true;
            }
            _logger.LogDebug("OverridePermission not found for deletion with Id={Id}", id);
            return false;
        }
    }
}
