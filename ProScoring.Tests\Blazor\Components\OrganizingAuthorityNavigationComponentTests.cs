using Microsoft.AspNetCore.Components;

namespace ProScoring.Tests.Blazor.Components;

/// <summary>
/// Test component for testing organizing authority navigation functionality.
/// Renders navigation buttons based on user permissions.
/// </summary>
public class OrganizingAuthorityNavigationComponentTests : ComponentBase
{
    /// <summary>
    /// Gets or sets whether the user has HMFIC (highest level) rights.
    /// </summary>
    [Parameter]
    public bool IsHmfic { get; set; }

    /// <summary>
    /// Renders the navigation buttons.
    /// </summary>
    protected override void BuildRenderTree(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder builder)
    {
        builder.OpenElement(0, "div");

        builder.OpenElement(1, "div");
        builder.AddAttribute(2, "data-testid", "oa-back-button");
        builder.CloseElement();

        if (IsHmfic)
        {
            builder.OpenElement(3, "div");
            builder.AddAttribute(4, "data-testid", "oa-list-button");
            builder.CloseElement();
        }

        builder.CloseElement();
    }
}
