using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services;

/// <summary>
/// Test stub class implementing IHasAutoInsertedId for testing purposes.
/// </summary>
public sealed class StubHasAutoInsertedId : IHasAutoInsertedId
{
    public string? Id { get; set; }
    public int IdLength => 8;
    public bool IdPadToLength => false;
    public string IdPrefix => "TEST";
}

/// <summary>
/// Tests for the CustomIdValueGenerator class, which handles ID generation for entities.
/// This test suite verifies the ID generation behavior for both entities that implement
/// IHasAutoInsertedId and those that don't.
/// </summary>
public sealed class CustomIdValueGeneratorTests : IDisposable
{
    #region Fields
    private readonly IIdGenerationUtilService _mockIdUtilsService;
    private readonly ILogger<CustomIdValueGenerator> _mockLogger;
    private readonly CustomIdValueGenerator _generator;
    private readonly TestDbContext _dbContext;
    #endregion

    #region Helper Classes
    /// <summary>
    /// Test DbContext for creating EntityEntry objects.
    /// </summary>
    private sealed class TestDbContext : DbContext
    {
        public TestDbContext(DbContextOptions<TestDbContext> options)
            : base(options) { }

        public DbSet<StubHasAutoInsertedId> StubEntities { get; set; } = null!;
        public DbSet<PlainTestEntity> PlainEntities { get; set; } = null!;
    }

    /// <summary>
    /// Plain entity that doesn't implement IHasAutoInsertedId for testing purposes.
    /// </summary>
    private sealed class PlainTestEntity
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
    }
    #endregion

    #region Constructor
    /// <summary>
    /// Initializes a new instance of the <see cref="CustomIdValueGeneratorTests"/> class.
    /// Sets up the test context with mocked dependencies.
    /// </summary>
    public CustomIdValueGeneratorTests()
    {
        _mockIdUtilsService = Substitute.For<IIdGenerationUtilService>();
        _mockLogger = Substitute.For<ILogger<CustomIdValueGenerator>>();
        _generator = new CustomIdValueGenerator(_mockIdUtilsService, _mockLogger);

        var options = new DbContextOptionsBuilder<TestDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        _dbContext = new TestDbContext(options);
    }
    #endregion

    #region Helper Methods
    /// <summary>
    /// Creates an EntityEntry for testing purposes.
    /// </summary>
    /// <param name="entity">The entity to create an entry for.</param>
    /// <returns>An EntityEntry for the specified entity.</returns>
    private EntityEntry CreateEntityEntry(object entity)
    {
        return _dbContext.Entry(entity);
    }
    #endregion

    #region Test Methods
    /// <summary>
    /// Tests that Next calls the ID utils service when the entity implements IHasAutoInsertedId.
    /// </summary>
    [Fact]
    public void Next_CallsIdUtilsService_WhenEntityImplementsIHasAutoInsertedId()
    {
        // Arrange
        var stubEntity = new StubHasAutoInsertedId();
        var entityEntry = CreateEntityEntry(stubEntity);

        var expectedId = "test-id";
        _mockIdUtilsService.GenerateId(stubEntity).Returns(expectedId);

        // Act
        var actualId = _generator.Next(entityEntry);

        // Assert
        _mockIdUtilsService.Received(1).GenerateId(Arg.Is<IHasAutoInsertedId>(e => e == stubEntity));
        Assert.Equal(expectedId, actualId);
    }

    /// <summary>
    /// Tests that Next generates a GUID and logs a warning when the entity doesn't implement IHasAutoInsertedId.
    /// </summary>
    [Fact]
    public void Next_GeneratesGuidAndLogsWarning_WhenEntityDoesNotImplementIHasAutoInsertedId()
    {
        // Arrange
        var plainEntity = new PlainTestEntity { Name = "Test" };
        var entityEntry = CreateEntityEntry(plainEntity);
        var expectedLogMessagePart =
            $"CustomIdValueGenerator is getting called for an entity of type {plainEntity.GetType().Name} that does not implement IHasAutoInsertedId";

        // Act
        var actualId = _generator.Next(entityEntry);

        // Assert
        _mockIdUtilsService.DidNotReceive().GenerateId(Arg.Any<IHasAutoInsertedId>());
        Assert.NotNull(actualId);
        Assert.True(Guid.TryParse(actualId.ToString(), out _), $"Generated ID '{actualId}' was not a valid GUID.");

        _mockLogger
            .Received(1)
            .Log(
                LogLevel.Warning,
                Arg.Any<EventId>(),
                Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessagePart)), // More direct state check
                null, // No exception expected
                Arg.Any<Func<object, Exception?, string>>()
            ); // Formatter
    }

    /// <summary>
    /// Tests that GeneratesTemporaryValues returns false.
    /// </summary>
    [Fact]
    public void GeneratesTemporaryValues_ReturnsFalse()
    {
        Assert.False(_generator.GeneratesTemporaryValues);
    }
    #endregion

    #region IDisposable Implementation
    /// <summary>
    /// Performs cleanup of resources used by the test class.
    /// </summary>
    public void Dispose()
    {
        _dbContext.Dispose();
        GC.SuppressFinalize(this);
    }
    #endregion
}
