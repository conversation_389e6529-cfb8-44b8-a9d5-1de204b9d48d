﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProScoring.Infrastructure.Database.PostgreSql_Migrations
{
    /// <inheritdoc />
    public partial class PostgreSql_AddDescriptionToOA : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RegattaExternalLinks_LinkType_LinkTypeId",
                table: "RegattaExternalLinks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LinkType",
                table: "LinkType");

            migrationBuilder.RenameTable(
                name: "LinkType",
                newName: "LinkTypes");

            migrationBuilder.AddColumn<string>(
                name: "ScopedEntityType",
                table: "UserAuthActions",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "Regattas",
                type: "character varying(60)",
                maxLength: 60,
                nullable: false,
                defaultValue: "")
                .Annotation("Relational:ColumnOrder", 167);

            migrationBuilder.AddColumn<decimal>(
                name: "EntryFee",
                table: "Regattas",
                type: "numeric",
                nullable: false,
                defaultValue: 0m)
                .Annotation("Relational:ColumnOrder", 175);

            migrationBuilder.AddColumn<decimal>(
                name: "LateFee",
                table: "Regattas",
                type: "numeric",
                nullable: false,
                defaultValue: 0m)
                .Annotation("Relational:ColumnOrder", 180);

            migrationBuilder.AddColumn<DateOnly>(
                name: "LateFeeDate",
                table: "Regattas",
                type: "date",
                nullable: true)
                .Annotation("Relational:ColumnOrder", 185);

            migrationBuilder.AddColumn<string>(
                name: "PostalCode",
                table: "Regattas",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "")
                .Annotation("Relational:ColumnOrder", 165);

            migrationBuilder.AddColumn<DateOnly>(
                name: "RegistrationCloseDate",
                table: "Regattas",
                type: "date",
                nullable: true)
                .Annotation("Relational:ColumnOrder", 190);

            migrationBuilder.AlterColumn<string>(
                name: "ImageId",
                table: "OrganizingAuthorities",
                type: "character varying(10)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(10)",
                oldNullable: true)
                .Annotation("Relational:ColumnOrder", 140)
                .OldAnnotation("Relational:ColumnOrder", 130);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "OrganizingAuthorities",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true)
                .Annotation("Relational:ColumnOrder", 130);

            migrationBuilder.AddPrimaryKey(
                name: "PK_LinkTypes",
                table: "LinkTypes",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "OverridePermissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "character varying(12)", maxLength: 12, nullable: false),
                    ActionName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ExpiresAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    IsAllowed = table.Column<bool>(type: "boolean", nullable: false),
                    Reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ScopedEntityType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    TargetId = table.Column<string>(type: "text", nullable: false),
                    UserId = table.Column<string>(type: "character varying(10)", nullable: false),
                    UpdatedById = table.Column<string>(type: "character varying(10)", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedById = table.Column<string>(type: "character varying(10)", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OverridePermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OverridePermissions_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OverridePermissions_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OverridePermissions_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OverridePermissions_CreatedById",
                table: "OverridePermissions",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_OverridePermissions_UpdatedById",
                table: "OverridePermissions",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_OverridePermissions_UserId",
                table: "OverridePermissions",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_RegattaExternalLinks_LinkTypes_LinkTypeId",
                table: "RegattaExternalLinks",
                column: "LinkTypeId",
                principalTable: "LinkTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RegattaExternalLinks_LinkTypes_LinkTypeId",
                table: "RegattaExternalLinks");

            migrationBuilder.DropTable(
                name: "OverridePermissions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LinkTypes",
                table: "LinkTypes");

            migrationBuilder.DropColumn(
                name: "ScopedEntityType",
                table: "UserAuthActions");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "EntryFee",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "LateFee",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "LateFeeDate",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "PostalCode",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "RegistrationCloseDate",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "OrganizingAuthorities");

            migrationBuilder.RenameTable(
                name: "LinkTypes",
                newName: "LinkType");

            migrationBuilder.AlterColumn<string>(
                name: "ImageId",
                table: "OrganizingAuthorities",
                type: "character varying(10)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(10)",
                oldNullable: true)
                .Annotation("Relational:ColumnOrder", 130)
                .OldAnnotation("Relational:ColumnOrder", 140);

            migrationBuilder.AddPrimaryKey(
                name: "PK_LinkType",
                table: "LinkType",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_RegattaExternalLinks_LinkType_LinkTypeId",
                table: "RegattaExternalLinks",
                column: "LinkTypeId",
                principalTable: "LinkType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
