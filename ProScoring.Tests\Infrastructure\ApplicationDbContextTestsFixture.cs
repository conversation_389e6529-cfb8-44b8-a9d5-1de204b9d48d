using ProScoring.Infrastructure.Database;

namespace ProScoring.Tests.Infrastructure;

/// <summary>
/// Test fixture for ApplicationDbContext tests.
/// Note: This code is currently not in use but kept for reference purposes
/// regarding DbContext setup in tests.
/// </summary>
public class ApplicationDbContextTestsFixture : IDisposable
{
    #region Fields
    private bool _disposed;
    private ServiceCollection? Services { get; set; }
    #endregion Fields

    #region Constructors
    /// <summary>
    /// Initializes a new instance of the <see cref="ApplicationDbContextTestsFixture"/> class.
    /// This constructor is kept for reference on how to set up DbContext for tests.
    /// </summary>
    public ApplicationDbContextTestsFixture()
    {
        // Set up the services for creating the db context. I don't want to use
        // the same DB context for each test, so we need to do the setup in the
        // test class, not the fixture.

        // This code in this class is not used, but I'm keeping it for now because
        // I may want to remember how to do it later.
    }
    #endregion Constructors

    #region IDisposable Implementation
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed)
        {
            return;
        }
        if (disposing)
        {
            // Dispose managed resources
        }
        // Dispose unmanaged resources
        _disposed = true;
    }
    #endregion IDisposable Implementation
}
