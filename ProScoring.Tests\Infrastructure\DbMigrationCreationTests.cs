using System;
using System.Linq;
using ProScoring.Infrastructure.Database;
using Xunit;

namespace ProScoring.Tests.Infrastructure;

/// <summary>
/// Tests to ensure migration context classes maintain consistency with the main ApplicationDbContext.
/// </summary>
public sealed class DbMigrationCreationTests
{
    /// <summary>
    /// Verifies that the PostgreSQL and SQLite creation contexts have the same properties as ApplicationDbContext.
    /// This ensures that any changes to ApplicationDbContext are reflected in the migration contexts.
    /// </summary>
    [Fact]
    public void PropertyDefinitions_AreConsistentAcrossContexts()
    {
        // Arrange
        var applicationDbContextProperties = typeof(ApplicationDbContext).GetProperties();
        var postgreSqlDbContextProperties = typeof(PostgreSqlCreationApplicationDbContext).GetProperties();
        var sqliteDbContextProperties = typeof(SqliteCreationApplicationDbContext).GetProperties();

        // Act
        var applicationDbContextPropertyNames = applicationDbContextProperties
            .Select(p => p.Name)
            .OrderBy(n => n)
            .ToList();
        var postgreSqlDbContextPropertyNames = postgreSqlDbContextProperties
            .Select(p => p.Name)
            .OrderBy(n => n)
            .ToList();
        var sqliteDbContextPropertyNames = sqliteDbContextProperties.Select(p => p.Name).OrderBy(n => n).ToList();

        // Assert
        Assert.Equal(applicationDbContextPropertyNames, postgreSqlDbContextPropertyNames);
        Assert.Equal(applicationDbContextPropertyNames, sqliteDbContextPropertyNames);
    }
}
