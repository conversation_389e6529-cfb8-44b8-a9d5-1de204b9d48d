# Organizing Authority (OA) Specification

This document provides details about the Organizing Authority (OA) object, associated pages, and backend components within the ProScoring application.

## 1. Organizing Authority Object (`ProScoring.Domain/Entities/OrganizingAuthority.cs`)

The `OrganizingAuthority` entity represents a club, association, or other organization that hosts and manages regattas.

**ID Prefix:** `O`

### Properties:

| Property Name  | Data Type     | Description                                                                 | Notes                                                                 |
|----------------|---------------|-----------------------------------------------------------------------------|-----------------------------------------------------------------------|
| `Id`           | `string` (max 10) | Unique identifier for the OA.                                               | Primary Key, uses `ID_PREFIX` "O".                                    |
| `Name`         | `string` (max 100) | Official name of the Organizing Authority.                                  | Required.                                                             |
| `Email`        | `string`      | Contact email address for the OA.                                           | `[EmailAddress]`, `[ProtectedPersonalData]`.                          |
| `Phone`        | `string`      | Contact phone number for the OA.                                            | `[Phone]`, `[ProtectedPersonalData]`.                                 |
| `Website`      | `string`      | Official website URL of the OA.                                             | `[Url]`.                                                              |
| `Private`      | `bool`        | If true, the OA might not be discoverable by general search (behavior TBD). |                                                                       |
| `AddressLine1` | `string` (max 100) | First line of the OA's address.                                           |                                                                       |
| `AddressLine2` | `string`      | Second line of the OA's address.                                            | Optional.                                                             |
| `City`         | `string` (max 100) | City of the OA's address.                                                 |                                                                       |
| `State`        | `string` (max 50) | State or province of the OA's address.                                      |                                                                       |
| `PostalCode`   | `string` (max 20) | Postal or ZIP code of the OA's address.                                   |                                                                       |
| `Country`      | `string` (max 60) | Country of the OA's address.                                              |                                                                       |
| `ImageId`      | `string?`     | Foreign key to the `FileRecord` for the OA's logo/burgee.                   | Optional.                                                             |
| `Image`        | `FileRecord?` (virtual) | Navigation property to the OA's logo/burgee.                              | Linked via `ImageId`.                                                 |
| `Approved`     | `bool`        | Indicates if the OA has been approved by an administrator. Default `false`. | HMFIC users can see/set this.                                         |

*Inherits from `LastChangeTrackingWithAutoInsertedIdBase` for auditing purposes (created/updated timestamps and user IDs).*

## 2. Organizing Authority Pages (Blazor Components)

Found in `ProScoring.Blazor/ProScoring.Blazor/Components/Pages/OrganizingAuthorityPages/`

### 2.1. Index Page (`Index.razor`)

*   **Route:** `/organizingauthorities`
*   **Purpose:** Displays a list of Organizing Authorities in a `RadzenDataGrid`. Allows filtering, sorting, and pagination. Provides actions to create, view details, edit, and delete OAs based on user permissions.
*   **Navigation:**
    *   Accessible from a main navigation link (assumed).
    *   Links to Create, Details, Edit, and Delete pages.
*   **Key Features:**
    *   **Grid Columns:** Approved (for HMFIC users), Burgee (image), Name (links to Details), Website, Phone, Email, Address components, Actions (Edit/Delete links).
    *   **"Add Organizing Authority" Button:** Navigates to `/organizingauthorities/create`.
    *   **Authorization:**
        *   Edit/Delete links are shown based on `AuthorizationService.AuthorizeAsync` checks against `EditAuthorizationForResourceHandler.PolicyName` (Note: Delete uses the same policy name as Edit in the `Index.razor` code, which might need review).
        *   "Approved" column visibility is controlled by HMFIC role check.
*   **Data Source:** `OrganizingAuthorityService.GetAllAsync()`.

### 2.2. Create Page (`Create.razor`)

*   **Route:** `/organizingauthorities/create`
*   **Purpose:** Allows users to create a new Organizing Authority.
*   **Navigation:**
    *   Accessed from the "Add Organizing Authority" button on the Index page.
    *   "Back" button navigates to previous page in history.
    *   "List" button (for HMFIC users) navigates to `/organizingauthorities`.
*   **Form Fields (`OrganizingAuthorityUploadDto`):**
    *   Private (Switch)
    *   Name (Text Input, Required)
    *   Website (Text Input, Required)
    *   Email (Text Input, Required)
    *   Phone (Text Input, Required)
    *   Address Line 1 (Text Input, Required)
    *   Address Line 2 (Text Input)
    *   City (Text Input, Required)
    *   State (Text Input, Custom validation for US addresses)
    *   Postal Code (Text Input, Required)
    *   Country (Text Input, Required)
    *   Burgee Upload (File Input, Optional, Max 1MB)
*   **Actions:**
    *   **Create (Submit Button):** Calls `organizingAuthorityService.CreateAsync(dto)`. Navigates to `/organizingauthorities` on success.
    *   An informational alert notes that OAs require administrator approval.
*   **Authorization:** Page itself is not explicitly protected by an `[Authorize]` attribute in the provided snippet, but the action of creating an OA and its subsequent usability would be subject to backend and approval logic. (Controller `Create` is authorized).

### 2.3. Details Page (`Details.razor`)

*   **Route:** `/organizingauthorities/details?id={Id}`
*   **Purpose:** Displays detailed information about a specific Organizing Authority.
*   **Navigation:**
    *   Accessed by clicking an OA's name on the Index page.
    *   "Edit" button (if authorized) navigates to the Edit page.
    *   "Back" button navigates to previous page in history.
    *   "List" button (for HMFIC users) navigates to `/organizingauthorities`.
*   **Information Displayed (Read-only):**
    *   Burgee image, Name
    *   Website, Email, Phone (as links)
    *   Full Address
    *   Private status
*   **Authorization:**
    *   The "Edit" button is shown/hidden based on `AuthorizeView` with `EditAuthorizationForPageWithIdHandler.PolicyName`.

### 2.4. Edit Page (`Edit.razor`)

*   **Route:** `/organizingauthorities/edit?id={Id}`
*   **Purpose:** Allows authorized users to edit the details of an existing Organizing Authority.
*   **Navigation:**
    *   Accessed from the "Edit" button on the Details page or Index page.
    *   "Back" button navigates to previous page in history.
    *   "List" button (for HMFIC users) navigates to `/organizingauthorities`.
*   **Form Fields (`OrganizingAuthorityUploadDto`):**
    *   Similar to Create page: Approved (Switch, for HMFIC), Private (Switch), Name, Website, Email, Phone, Address fields.
    *   Option to "Change Burgee" which shows a file input.
*   **Actions:**
    *   **Save (Submit Button):** Calls `OrganizingAuthorityService.UpdateAsync(dto)`. Navigates to `/organizingauthorities` on success.
*   **Authorization:**
    *   Page is protected by `[Authorize(Policy = EditAuthorizationForPageWithIdHandler.PolicyName)]`.
    *   "Approved" switch is only visible/editable by HMFIC users.

### 2.5. Delete Page (`Delete.razor`)

*   **Route:** `/organizingauthorities/delete?id={Id}`
*   **Purpose:** Allows authorized users to delete an Organizing Authority after confirmation.
*   **Navigation:**
    *   Accessed from the "Delete" link on the Index page.
*   **Content:**
    *   Displays a confirmation message: "Are you sure you want to delete {OA Name}?"
*   **Actions:**
    *   **Delete (Button):** Calls `OrganizingAuthorityService.DeleteAsync(Id)`. Navigates to `/organizingauthorities` on success.
    *   **Cancel (Button):** Navigates to `/organizingauthorities`.
*   **Authorization:**
    *   Page is protected by `[Authorize(Policy = DeleteAuthorizationForPageWithIdHandler.PolicyName)]`.

## 3. Organizing Authority Controller (`ProScoring.Blazor/Controllers/OrganizingAuthorityController.cs`)

*   **Route Prefix:** `api/OrganizingAuthority`
*   **Purpose:** Handles HTTP requests related to Organizing Authority management. It uses `IOrganizingAuthorityService` for business logic and `IFileService` for image uploads.
*   **Key Endpoints:**
    *   **`POST /api/OrganizingAuthority` (`Create` method):**
        *   Accepts `OrganizingAuthorityUploadDto`.
        *   Handles burgee image upload via `IFileService`.
        *   Calls `_service.CreateAsync(oa)`.
        *   Grants the creator ADMIN rights to the new OA via `_authorizationService.CreateUserAuthActionAsync`.
        *   Returns `201 Created`.
    *   **`DELETE /api/OrganizingAuthority/{id}` (`Delete` method):**
        *   Calls `_service.DeleteAsync(id)`. Returns `204 NoContent`.
    *   **`GET /api/OrganizingAuthority` (`GetAll` method):**
        *   Returns a list of all OAs via `_service.GetAllAsync()`.
    *   **`GET /api/OrganizingAuthority/paged` (`GetPagedList` method):**
        *   Returns a paged list of OAs with sorting and filtering options.
    *   **`GET /odata/OrganizingAuthorities` (`GetOData` method):**
        *   Provides OData query capabilities over OAs via `_service.GetFilteredQueryableForODataAsync()`.
    *   **`GET /api/OrganizingAuthority/{id}` (`GetById` method):**
        *   Returns a specific OA by ID.
    *   **`PUT /api/OrganizingAuthority/{id}` (`Update` method):**
        *   Accepts an `OrganizingAuthority` object to update an existing OA.
    *   **`PATCH /odata/OrganizingAuthorities({id})` (`Patch` method):**
        *   Allows partial updates to an OA (currently uses the same full update logic as PUT).
    *   **`GET /api/OrganizingAuthority/states` (`GetAllStates` method):**
        *   Returns a list of unique states from all OAs.
    *   **`GET /api/OrganizingAuthority/countries` (`GetAllCountries` method):**
        *   Returns a list of unique countries from all OAs.
*   **Authorization:** While the controller itself isn't globally decorated with `[Authorize]` in the provided snippet (unlike `RegattaController`), individual actions would rely on service-layer authorization checks and the authorization policies applied to the Blazor pages that consume these APIs. The `Create` method, for example, explicitly sets up initial permissions for the creator.

This document should provide a good overview of the Organizing Authority features.

## 4. Shared Components Related to Organizing Authorities

In addition to the dedicated pages in `ProScoring.Blazor/ProScoring.Blazor/Components/Pages/OrganizingAuthorityPages/`, there are components in `ProScoring.Blazor/ProScoring.Blazor/Components/Shared/` that are relevant to Organizing Authorities:

### 4.1. `OrganizingAuthoritiesList.razor`

*   **Purpose:** Provides a feature-rich list display for Organizing Authority Information DTOs (`OrganizingAuthorityInfoDto`). This component appears to be an alternative or more advanced version compared to the `Index.razor` page found in the `OrganizingAuthorityPages` directory.
*   **Key Features:**
    *   Uses `RadzenDataList` with virtualization and paging.
    *   Fetches data via `IOrganizingAuthorityHttpClient` using OData queries.
    *   Includes a collapsible filter panel (`RadzenDataFilter`) for Name, City, State, and Country.
    *   Renders each OA using the `OrganizingAuthorityInfo.razor` component.
    *   Supports `DisplayMode.Card` and `DisplayMode.Row` for item rendering, switchable by the user.
    *   Adapts to screen size (e.g., forces row view on small screens).
    *   Persists user preferences for page size and display mode in local storage.
*   **Note:** The relationship and intended usage between this shared list component and the `Pages/OrganizingAuthorityPages/Index.razor` should be clarified. This shared component offers more advanced filtering and display options.

### 4.2. `OrganizingAuthorityInfo.razor`

*   **Purpose:** A reusable component designed to display information for a single `OrganizingAuthorityInfoDto`.
*   **Parameters:**
    *   `Authority` (OrganizingAuthorityInfoDto): The OA data to display.
    *   `DisplayMode` (enum: `Card` or `Row`): Controls the layout.
    *   `ShowAdditionalInfo` (bool): Toggles visibility of contact details like email, phone, website.
    *   `EnableNavigationOnClick` (bool): If true, clicking the component navigates to the OA's details page.
*   **Functionality:** Renders OA details (burgee, name, location, optional contacts) differently based on the selected `DisplayMode`.

### 4.3. `DisplayMode.cs`

*   **Purpose:** Defines an enum `DisplayMode` with two values:
    *   `Card`: For card-based layouts.
    *   `Row`: For list or row-based layouts.
*   **Usage:** Used by `OrganizingAuthorityInfo.razor` and `OrganizingAuthoritiesList.razor` to control rendering style.

*(Note: A generic `FileUpload.razor` component also exists in Shared, but current OA and Regatta pages seem to use `RadzenFileInput` directly for their specific upload needs.)*
