using Bunit;
using FluentAssertions;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;
using ProScoring.Blazor.Extensions;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the AsTestId extension method.
/// </summary>
public class TestIdTests : TestContext
{
    /// <summary>
    /// Verifies that the AsTestId extension method correctly adds a data-testid attribute to an element.
    /// </summary>
    [Fact]
    public void AsTestId_ShouldAddDataTestIdAttribute()
    {
        // Arrange & Act
        var testComponent = RenderComponent<TestIdTestComponent>();

        // Assert
        var div = testComponent.Find("div");
        div.Should().NotBeNull();
        div.GetAttribute("data-testid").Should().Be("test-div");
    }

    /// <summary>
    /// A test component that renders a div element with a data-testid attribute.
    /// </summary>
    private class TestIdTestComponent : ComponentBase
    {
        protected override void BuildRenderTree(RenderTreeBuilder builder)
        {
            builder.OpenElement(0, "div");
            builder.AddMultipleAttributes(1, "test-div".AsTestId());
            builder.AddContent(2, "Test");
            builder.CloseElement();
        }
    }
}
