using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Represents an application user with additional properties for tracking creation and updates.
/// </summary>
public class ApplicationUser
    : IdentityUser,
        IHasAutoInsertedId,
        IHasLastChangeTracking,
        IHasInitialSeedData<ApplicationUser>
{
    private const string ID_PREFIX = "U";
    private readonly ILogger<ApplicationUser>? _logger;
    private string? _givenName;
    private string? _surname;

    // constructor overrides setting if Id from base constructor.
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
    public ApplicationUser()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        : base()
    {
        try
        {
            // Override the IdentityUser base constructor which sets Id to a GUID
            // We need to set it to null so Entity Framework will call our value generator
            Id = null!;
            // No logging here since _logger is not initialized
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error in ApplicationUser constructor: {ex.Message}");
            // We can't log here because logger isn't initialized
            throw;
        }
    }

    /// <summary>
    /// Constructor with logger for error handling
    /// </summary>
    /// <param name="logger">Logger instance for this class</param>
    public ApplicationUser(ILogger<ApplicationUser> logger)
        : this()
    {
        try
        {
            _logger = logger;
            logger.LogTrace("ApplicationUser constructed with null Id, waiting for CustomIdValueGenerator");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error in ApplicationUser constructor with logger: {ex.Message}");
            // We can't log here because logger isn't initialized
            throw;
        }
    }

    #region Properties

    // I make this nullable here, because it will be set when saved because of the IHasAutoInsertedGuidishId interface
    // for this reason I needed to disable warnings.
    [MaxLength(10)]
    [Column(Order = 10)]
    public override string? Id
    {
#pragma warning disable CS8764 // Nullability of return type doesn't match overridden member (possibly because of nullability attributes).
        get => base.Id;
#pragma warning restore CS8764 // Nullability of return type doesn't match overridden member (possibly because of nullability attributes).
#pragma warning disable CS8601 // Possible null reference assignment.
        set
        {
            try
            {
                _logger?.LogTrace("Setting Id: {Id}", value);
                base.Id = value;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error setting Id property");
                Debug.WriteLine($"Error in Id setter: {ex.Message}");
                throw;
            }
        }
#pragma warning restore CS8601 // Possible null reference assignment.
    }

    /// <summary>
    /// Gets or sets the user's given name (first name). Maximum length is 50 characters.
    /// </summary>
    [MaxLength(50)]
    [Column(Order = 20)]
    [ProtectedPersonalData]
    public string? GivenName
    {
        get => _givenName;
        set
        {
            try
            {
                if (value?.Length > 50)
                {
                    _logger?.LogWarning(
                        "GivenName value exceeds maximum length of 50 characters. Value will be truncated."
                    );
                    value = value[..50];
                }
                _logger?.LogTrace("Setting GivenName: {GivenName}", value);
                _givenName = value;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error setting GivenName property");
                Debug.WriteLine($"Error in GivenName setter: {ex.Message}");
                throw;
            }
        }
    }

    /// <summary>
    /// Gets or sets the user's surname (family name/last name). Maximum length is 50 characters.
    /// </summary>
    [MaxLength(50)]
    [Column(Order = 30)]
    [ProtectedPersonalData]
    public string? Surname
    {
        get => _surname;
        set
        {
            try
            {
                if (value?.Length > 50)
                {
                    _logger?.LogWarning(
                        "Surname value exceeds maximum length of 50 characters. Value will be truncated."
                    );
                    value = value[..50];
                }
                _logger?.LogTrace("Setting Surname: {Surname}", value);
                _surname = value;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error setting Surname property");
                Debug.WriteLine($"Error in Surname setter: {ex.Message}");
                throw;
            }
        }
    }

    [Column(Order = 40)]
    //Override properties to be able to control column order
    public override string? UserName
    {
        get => base.UserName;
        set
        {
            try
            {
                _logger?.LogTrace("Setting UserName: {UserName}", value);
                base.UserName = value;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error setting UserName property");
                Debug.WriteLine($"Error in UserName setter: {ex.Message}");
                throw;
            }
        }
    }

    [Column(Order = 50)]
    //Override properties to be able to control column order
    [ProtectedPersonalData]
    public override string? Email
    {
        get => base.Email;
        set
        {
            try
            {
                _logger?.LogTrace("Setting Email: {Email}", value);
                base.Email = value;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error setting Email property");
                Debug.WriteLine($"Error in Email setter: {ex.Message}");
                throw;
            }
        }
    }

    [Column(Order = 60)]
    //Override properties to be able to control column order
    [ProtectedPersonalData]
    public override string? PhoneNumber
    {
        get => base.PhoneNumber;
        set
        {
            try
            {
                _logger?.LogTrace("Setting PhoneNumber: {PhoneNumber}", value);
                base.PhoneNumber = value;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error setting PhoneNumber property");
                Debug.WriteLine($"Error in PhoneNumber setter: {ex.Message}");
                throw;
            }
        }
    }

    [Column(Order = 1000)]
    //Override properties to be able to control column order
    public override int AccessFailedCount
    {
        get => base.AccessFailedCount;
        set
        {
            try
            {
                _logger?.LogTrace("Setting AccessFailedCount: {AccessFailedCount}", value);
                base.AccessFailedCount = value;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error setting AccessFailedCount property");
                Debug.WriteLine($"Error in AccessFailedCount setter: {ex.Message}");
                throw;
            }
        }
    }

    /// <summary>
    /// Gets the user's full name as a combination of given name and surname.
    /// This property is not persisted to the database.
    /// </summary>
    [NotMapped]
    public string? Name
    {
        get
        {
            try
            {
                _logger?.LogTrace("Getting full Name: {FullName}", $"{GivenName} {Surname}");
                if (string.IsNullOrEmpty(GivenName) && string.IsNullOrEmpty(Surname))
                    return null;

                if (string.IsNullOrEmpty(GivenName))
                    return Surname;

                if (string.IsNullOrEmpty(Surname))
                    return GivenName;

                return string.Join(" ", GivenName, Surname);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error retrieving full Name property");
                Debug.WriteLine($"Error in Name getter: {ex.Message}");
                return null; // Return null if there's an error to avoid exceptions
            }
        }
    }

    #endregion // Properties

    #region IHasAutoInsertedId Implementation

    public int IdLength => 8;

    public bool IdPadToLength => false;

    public string IdPrefix => ID_PREFIX;

    #endregion // IHasAutoInsertedId Implementation

    #region IHasLastChangeTracking Implementation

    /// <summary>
    /// Gets or sets the ID of the user who created this user.
    /// </summary>
    [ForeignKey(nameof(CreatedBy))]
    [Column(Order = 1040)]
    public string? CreatedById { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the user was created.
    /// </summary>
    [Column(Order = 1050)]
    public DateTimeOffset CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the user who created this user.
    /// </summary>
    public virtual ApplicationUser? CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the ID of the user who last updated this user.
    /// </summary>
    [ForeignKey(nameof(UpdatedBy))]
    [Column(Order = 1010)]
    public string? UpdatedById { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the user was last updated.
    /// </summary>
    [Column(Order = 1020)]
    public DateTimeOffset UpdatedAt { get; set; }

    /// <summary>
    /// Gets or sets the user who last updated this user.
    /// </summary>
    public virtual ApplicationUser? UpdatedBy { get; set; }

    #endregion // IHasLastChangeTracking Implementation

    #region SeedData

    /// <summary>
    /// Gets the seed data for the ApplicationUser entity including the null user.
    /// </summary>
    public static ApplicationUser[] SeedData =>
        new[]
        {
            new ApplicationUser
            {
                Id = "U000000000",
                UserName = "<EMAIL>",
                NormalizedUserName = "<EMAIL>",
                Email = "<EMAIL>",
                NormalizedEmail = "<EMAIL>",
                EmailConfirmed = true,
                PasswordHash = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                SecurityStamp = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
                ConcurrencyStamp = "00000000-0000-0000-0000-000000000000",
                PhoneNumberConfirmed = false,
                TwoFactorEnabled = false,
                LockoutEnabled = true,
                AccessFailedCount = 0,
                CreatedById = "U000000000",
                UpdatedById = "U000000000",
                CreatedAt = new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero),
                UpdatedAt = new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero),
            },
        };

    #endregion // SeedData
}
