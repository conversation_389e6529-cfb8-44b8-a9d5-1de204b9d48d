using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.Services;

namespace ProScoring.Tests.Infrastructure.Services
{
    /// <summary>
    /// Tests for the EmailSenderService class which handles sending various email notifications
    /// such as confirmation links, password reset codes, and password reset links.
    /// </summary>
    public class EmailSenderServiceTests
    {
        private readonly IEmailSender _actualSenderSubstitute;
        private readonly ILogger<EmailSenderService> _loggerSubstitute;
        private readonly ApplicationUser _testUser;
        private EmailSenderService _emailSenderService = null!;

        public EmailSenderServiceTests()
        {
            _actualSenderSubstitute = Substitute.For<IEmailSender>();
            _loggerSubstitute = Substitute.For<ILogger<EmailSenderService>>();
            _testUser = new ApplicationUser { Email = "<EMAIL>", UserName = "testuser" };
        }

        private void SetupEmailOptions(
            bool enabled,
            string apiKey = "test_api_key",
            string fromAddress = "<EMAIL>",
            string fromName = "Test App"
        )
        {
            var configData = new Dictionary<string, string?>
            {
                [$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.Enabled)}"] = enabled.ToString(),
                [$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.ApiKey)}"] = apiKey,
                [$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromAddress)}"] = fromAddress,
                [$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromName)}"] = fromName,
            };

            var configuration = new ConfigurationBuilder().AddInMemoryCollection(configData).Build();

            _emailSenderService = new EmailSenderService(_actualSenderSubstitute, configuration, _loggerSubstitute);
        }

        #region SendConfirmationLinkAsync Tests
        [Fact]
        public async Task SendConfirmationLinkAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled, email would have been <NAME_EMAIL>";

            // Act
            await _emailSenderService.SendConfirmationLinkAsync(_testUser, "<EMAIL>", "confirm_link");

            // Assert
            await _actualSenderSubstitute
                .DidNotReceive()
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>());

            _loggerSubstitute
                .Received(1)
                .Log(
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state != null && state.ToString()!.Contains(expectedLogMessage)),
                    null,
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }

        [Fact]
        public async Task SendConfirmationLinkAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendConfirmationLinkAsync(_testUser, "<EMAIL>", "confirm_link");

            // Assert
            await _actualSenderSubstitute
                .Received(1)
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>());
        }
        #endregion SendConfirmationLinkAsync Tests

        #region SendPasswordResetCodeAsync Tests
        [Fact]
        public async Task SendPasswordResetCodeAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled, email would have been <NAME_EMAIL>";

            // Act
            await _emailSenderService.SendPasswordResetCodeAsync(_testUser, "<EMAIL>", "reset_code");

            // Assert
            await _actualSenderSubstitute
                .DidNotReceive()
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>());

            _loggerSubstitute
                .Received(1)
                .Log(
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state != null && state.ToString()!.Contains(expectedLogMessage)),
                    null,
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }

        [Fact]
        public async Task SendPasswordResetCodeAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendPasswordResetCodeAsync(_testUser, "<EMAIL>", "reset_code");

            // Assert
            await _actualSenderSubstitute
                .Received(1)
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>());
        }
        #endregion SendPasswordResetCodeAsync Tests

        #region SendPasswordResetLinkAsync Tests
        [Fact]
        public async Task SendPasswordResetLinkAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled, email would have been <NAME_EMAIL>";

            // Act
            await _emailSenderService.SendPasswordResetLinkAsync(_testUser, "<EMAIL>", "reset_link");

            // Assert
            await _actualSenderSubstitute
                .DidNotReceive()
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>());

            _loggerSubstitute
                .Received(1)
                .Log(
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state != null && state.ToString()!.Contains(expectedLogMessage)),
                    null,
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }

        [Fact]
        public async Task SendPasswordResetLinkAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendPasswordResetLinkAsync(_testUser, "<EMAIL>", "reset_link");

            // Assert
            await _actualSenderSubstitute
                .Received(1)
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>());
        }
        #endregion SendPasswordResetLinkAsync Tests
    }
}
