using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.Authorization.Repositories.Interfaces;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces; // Added for ITargetHierarchyService

namespace ProScoring.Infrastructure.Authorization;

public class ProScoringAuthorizationService : IProScoringAuthorizationService
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<ProScoringAuthorizationService> _logger;
    private readonly IUserAuthActionRepository _userAuthActionRepository;
    private readonly IOverridePermissionRepository _overridePermissionRepository;
    private readonly ITargetHierarchyService _targetHierarchyService; // Added field

    public ProScoringAuthorizationService(
        IApplicationDbContext context,
        ILogger<ProScoringAuthorizationService> logger,
        IUserAuthActionRepository userAuthActionRepository,
        IOverridePermissionRepository overridePermissionRepository,
        ITargetHierarchyService targetHierarchyService
    ) // Added parameter
    {
        _context = context;
        _logger = logger;
        _userAuthActionRepository = userAuthActionRepository;
        _overridePermissionRepository = overridePermissionRepository;
        _targetHierarchyService = targetHierarchyService; // Added assignment
        _logger.LogInformation(
            "ProScoringAuthorizationService initialized with repositories and target hierarchy service"
        );
    }

    #region methods

    /// <summary>
    /// Creates a new UserAuthAction.
    /// </summary>
    /// <param name="actorId">The ID of the user.</param>
    /// <param name="targetId">The ID of the target.</param>
    /// <param name="action">The action to create.</param>
    /// <returns>The created UserAuthAction.</returns>
    public virtual async Task<UserAuthAction> CreateUserAuthActionAsync(string actorId, string targetId, string action)
    {
        try
        {
            _logger.LogDebug(
                "Creating user auth action. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                action
            );

            await ValidateUserIdAsync(actorId);
            await ValidateAuthActionAsync(action);
            await ValidateTargetIdAsync(targetId);
            await ValidateUserAuthActionIsNewAsync(actorId, targetId, action);

            var userAuthAction = new UserAuthAction
            {
                AuthActionName = action,
                UserId = actorId,
                TargetId = targetId,
            };

            var targetTypeName = (await GetTargetTypeAsync(targetId))!.Name;

            _logger.LogInformation(
                "Creating UserAuthAction for user {UserId} with action {Action} on {TargetType} {TargetId}",
                actorId,
                action,
                targetTypeName,
                targetId
            );

            _context.UserAuthActions.Add(userAuthAction);
            await _context.SaveChangesAsync();
            _logger.LogInformation("UserAuthAction created successfully");
            return userAuthAction;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(
                ex,
                "Validation failed while creating user auth action. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                action
            );
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(
                ex,
                "Database error while creating user auth action. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                action
            );
            throw new InvalidOperationException(
                $"Failed to create authorization for user {actorId} on target {targetId} with action {action}",
                ex
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error while creating user auth action. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                action
            );
            throw new InvalidOperationException(
                $"Failed to create authorization for user {actorId} on target {targetId} with action {action}",
                ex
            );
        }
    }

    /// <summary>
    /// Creates multiple UserAuthActions.
    /// </summary>
    /// <param name="userId">The ID of the user.</param>
    /// <param name="targetIds">The IDs of the targets.</param>
    /// <param name="action">The action to create.</param>
    /// <returns>A list of created UserAuthActions.</returns>
    public async Task<List<UserAuthAction>> CreateUserAuthActionsAsync(
        string userId,
        IEnumerable<string> targetIds,
        string action
    )
    {
        try
        {
            _logger.LogDebug(
                "Creating multiple user auth actions. UserId: {UserId}, Action: {Action}, TargetCount: {Count}",
                userId,
                action,
                targetIds.Count()
            );

            await ValidateUserIdAsync(userId);
            await ValidateAuthActionAsync(action);
            await ValidateTargetIdsAsync(targetIds);
            await ValidateUserAuthActionExistsAsync(userId, targetIds, action);

            var userAuthActions = targetIds
                .Select(targetId => new UserAuthAction
                {
                    AuthActionName = action,
                    UserId = userId,
                    TargetId = targetId,
                })
                .ToList();

            foreach (var targetId in userAuthActions.Select(userAuthAction => userAuthAction.TargetId))
            {
                _logger.LogInformation(
                    "Creating UserAuthAction for user {UserId} with action {Action} on {TargetType} {TargetId}",
                    userId,
                    action,
                    (await GetTargetTypeAsync(targetId))!.Name,
                    targetId
                );
            }

            _context.UserAuthActions.AddRange(userAuthActions);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Created {Count} user auth actions successfully", userAuthActions.Count);
            return userAuthActions;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(
                ex,
                "Validation failed while creating multiple user auth actions. UserId: {UserId}, Action: {Action}, TargetCount: {Count}",
                userId,
                action,
                targetIds.Count()
            );
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(
                ex,
                "Database error while creating multiple user auth actions. UserId: {UserId}, Action: {Action}",
                userId,
                action
            );
            throw new InvalidOperationException(
                $"Failed to create authorizations for user {userId} with action {action}",
                ex
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error while creating multiple user auth actions. UserId: {UserId}, Action: {Action}, TargetCount: {Count}",
                userId,
                action,
                targetIds.Count()
            );
            throw new InvalidOperationException(
                $"Failed to create authorizations for user {userId} with action {action}",
                ex
            );
        }
    }

    public async Task<List<UserAuthAction>> CreateUserAuthActionsAsync(
        IEnumerable<(string UserId, string TargetId, string Action)> sets
    )
    {
        try
        {
            _logger.LogDebug("Creating batch user auth actions. Count: {Count}", sets.Count());

            await ValidateUserIdsAsync(sets.Select(s => s.UserId));
            await ValidateActionsAsync(sets.Select(s => s.Action));
            await ValidateTargetIdsAsync(sets.Select(s => s.TargetId));

            var userAuthActions = sets.Select(s => new UserAuthAction
                {
                    AuthActionName = s.Action,
                    UserId = s.UserId,
                    TargetId = s.TargetId,
                })
                .ToList();

            foreach (var userAuthAction in userAuthActions)
            {
                _logger.LogInformation(
                    "Creating UserAuthAction for user {UserId} with action {Action} on {TargetType} {TargetId}",
                    userAuthAction.UserId,
                    userAuthAction.AuthActionName,
                    (await GetTargetTypeAsync(userAuthAction.TargetId))!.Name,
                    userAuthAction.TargetId
                );
            }
            _context.UserAuthActions.AddRange(userAuthActions);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Created {Count} user auth actions successfully", userAuthActions.Count);
            return userAuthActions;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(
                ex,
                "Validation failed while creating batch user auth actions. Count: {Count}",
                sets.Count()
            );
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "Database error while creating batch user auth actions. Count: {Count}", sets.Count());
            throw new InvalidOperationException("Failed to create batch authorizations", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error while creating batch user auth actions. Count: {Count}",
                sets.Count()
            );
            throw new InvalidOperationException("Failed to create batch authorizations", ex);
        }
    }

    /// <summary>
    /// Deletes all UserAuthActions for a given user and target.
    /// </summary>
    /// <param name="actorId">The ID of the user.</param>
    /// <param name="targetId">The ID of the target.</param>
    /// <returns>The number of UserAuthActions deleted.</returns>
    public async Task<int> DeleteAllUserAuthActionsForUserTargetAsync(string actorId, string targetId)
    {
        try
        {
            _logger.LogDebug(
                "Deleting all user auth actions for user {UserId} on target {TargetId}",
                actorId,
                targetId
            );

            var userAuthActions = await _context
                .UserAuthActions.Where(uaa => uaa.UserId == actorId && uaa.TargetId == targetId)
                .ToListAsync();

            foreach (var userAuthAction in userAuthActions)
            {
                _logger.LogInformation(
                    "Deleting UserAuthAction {Action} for user {UserId} on target {TargetId}",
                    userAuthAction.AuthActionName,
                    actorId,
                    targetId
                );
            }

            if (userAuthActions.Count != 0)
            {
                _context.UserAuthActions.RemoveRange(userAuthActions);
                await _context.SaveChangesAsync();
            }
            _logger.LogInformation("Deleted {Count} user auth actions", userAuthActions.Count);
            return userAuthActions.Count;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(
                ex,
                "Database error while deleting user auth actions. UserId: {UserId}, TargetId: {TargetId}",
                actorId,
                targetId
            );
            throw new InvalidOperationException(
                $"Failed to delete authorizations for user {actorId} on target {targetId}",
                ex
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error while deleting user auth actions. UserId: {UserId}, TargetId: {TargetId}",
                actorId,
                targetId
            );
            throw new InvalidOperationException(
                $"Failed to delete authorizations for user {actorId} on target {targetId}",
                ex
            );
        }
    }

    public async Task DeleteUserAuthActionAsync(string actorId, string targetId, string actionName)
    {
        try
        {
            _logger.LogDebug(
                "Deleting user auth action. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                actionName
            );

            var userAuthAction = await _context.UserAuthActions.FindAsync(actorId, targetId, actionName);

            if (userAuthAction == null)
            {
                _logger.LogWarning(
                    "UserAuthAction not found for deletion. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                    actorId,
                    targetId,
                    actionName
                );
                throw new InvalidOperationException("UserAuthAction does not exist for this user");
            }

            _logger.LogInformation(
                "Deleting UserAuthAction {Action} for user {UserId} on target {TargetId}",
                actionName,
                actorId,
                targetId
            );

            _context.UserAuthActions.Remove(userAuthAction);
            await _context.SaveChangesAsync();
            _logger.LogInformation("UserAuthAction deleted successfully");
        }
        catch (InvalidOperationException)
        {
            // Already logged above
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(
                ex,
                "Database error while deleting user auth action. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                actionName
            );
            throw new InvalidOperationException(
                $"Failed to delete authorization for user {actorId} on target {targetId} with action {actionName}",
                ex
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error while deleting user auth action. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                actionName
            );
            throw new InvalidOperationException(
                $"Failed to delete authorization for user {actorId} on target {targetId} with action {actionName}",
                ex
            );
        }
    }

    /// <summary>
    /// Deletes multiple UserAuthActions by pairs of userId and targetId.
    /// </summary>
    /// <param name="keyPairs">The collection of (UserId, TargetId) pairs to delete.</param>
    /// <returns>The number of UserAuthActions deleted.</returns>
    public async Task<int> DeleteUserAuthActionsAsync(IEnumerable<(string UserId, string TargetId)> keyPairs)
    {
        try
        {
            _logger.LogDebug("Deleting user auth actions by key pairs. Count: {Count}", keyPairs.Count());

            var userAuthActions = await _context
                .UserAuthActions.Where(uaa => keyPairs.Any(p => p.UserId == uaa.UserId && p.TargetId == uaa.TargetId))
                .ToListAsync();

            foreach (var keyPair in keyPairs)
            {
                _logger.LogInformation(
                    "Deleting UserAuthActions for user {UserId} on target {TargetId}",
                    keyPair.UserId,
                    keyPair.TargetId
                );
            }

            if (userAuthActions.Count > 0)
            {
                _context.UserAuthActions.RemoveRange(userAuthActions);
                await _context.SaveChangesAsync();
            }
            _logger.LogInformation("Deleted {Count} user auth actions", userAuthActions.Count);
            return userAuthActions.Count;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(
                ex,
                "Database error while deleting user auth actions by key pairs. Count: {Count}",
                keyPairs.Count()
            );
            throw new InvalidOperationException("Failed to delete authorizations", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error while deleting user auth actions by key pairs. Count: {Count}",
                keyPairs.Count()
            );
            throw new InvalidOperationException("Failed to delete authorizations", ex);
        }
    }

    public async Task DeleteUserAuthActionsAsync(IEnumerable<(string UserId, string TargetId, string Action)> sets)
    {
        try
        {
            _logger.LogDebug("Deleting specific user auth actions. Count: {Count}", sets.Count());

            var userAuthActions = new List<UserAuthAction>(); // check that all this values exist
            foreach (var set in sets)
            {
                try
                {
                    var userAuthAction = await _context.UserAuthActions.FindAsync(set.UserId, set.TargetId, set.Action);
                    if (userAuthAction == null)
                    {
                        _logger.LogWarning(
                            "UserAuthAction not found for deletion. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                            set.UserId,
                            set.TargetId,
                            set.Action
                        );
                        throw new InvalidOperationException(
                            "One or more of these UserAuthAction does not exist for this user"
                        );
                    }
                    userAuthActions.Add(userAuthAction);
                }
                catch (Exception ex) when (ex is not InvalidOperationException)
                {
                    _logger.LogError(
                        ex,
                        "Error finding UserAuthAction. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                        set.UserId,
                        set.TargetId,
                        set.Action
                    );
                    throw new InvalidOperationException(
                        $"Error finding authorization for user {set.UserId} on target {set.TargetId} with action {set.Action}",
                        ex
                    );
                }
            }
            foreach (var userAuthAction in userAuthActions)
            {
                _logger.LogInformation(
                    "Deleting UserAuthAction for user {UserId} on target {TargetId} with action {Action}",
                    userAuthAction.UserId,
                    userAuthAction.TargetId,
                    userAuthAction.AuthActionName
                );
            }
            _context.UserAuthActions.RemoveRange(userAuthActions);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Deleted {Count} user auth actions", userAuthActions.Count);
        }
        catch (InvalidOperationException)
        {
            // Already logged above
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(
                ex,
                "Database error while deleting specific user auth actions. Count: {Count}",
                sets.Count()
            );
            throw new InvalidOperationException("Failed to delete specified authorizations", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error while deleting specific user auth actions. Count: {Count}",
                sets.Count()
            );
            throw new InvalidOperationException("Failed to delete specified authorizations", ex);
        }
    }

    public virtual async Task<IList<AuthAction>?> GetActionsAsync(string actorId, string targetId)
    {
        try
        {
            _logger.LogDebug("Getting actions for user {UserId} on target {TargetId}", actorId, targetId);

            var userActions = await _context
                .UserAuthActions.Where(uaa =>
                    uaa.UserId == actorId && (uaa.TargetId == targetId || uaa.TargetId == AuthTypes.UNIVERSAL_TARGET)
                )
                .Select(uaa => uaa.AuthAction)
                .ToListAsync();

            _logger.LogDebug(
                "Found {Count} actions for user {UserId} on target {TargetId}",
                userActions.Count,
                actorId,
                targetId
            );
            return userActions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting actions for user {UserId} on target {TargetId}", actorId, targetId);
            throw new InvalidOperationException($"Failed to get actions for user {actorId} on target {targetId}", ex);
        }
    }

    /// <summary>
    /// Gets the allowed actions for a given actor and target.
    /// </summary>
    /// <param name="actorId">The ID of the actor.</param>
    /// <param name="targetId">The ID of the target.</param>
    /// <returns>A list of allowed actions.</returns>
    public async Task<IList<AuthAction>> GetAllowedActionsAsync(string actorId, string targetId)
    {
        try
        {
            _logger.LogDebug("Getting allowed actions for user {UserId} on target {TargetId}", actorId, targetId);

            var authActions = await _context
                .UserAuthActions.Where(uaa =>
                    uaa.UserId == actorId && (uaa.TargetId == targetId || uaa.TargetId == AuthTypes.UNIVERSAL_TARGET)
                )
                .Select(uaa => uaa.AuthAction)
                .ToListAsync();

            _logger.LogDebug("Found {Count} direct actions, expanding action hierarchy", authActions.Count);
            var expandedActions = await ExpandActionsAsync(authActions);
            _logger.LogDebug("Expanded to {Count} total allowed actions", expandedActions.Count);

            return expandedActions;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error getting allowed actions for user {UserId} on target {TargetId}",
                actorId,
                targetId
            );
            throw new InvalidOperationException(
                $"Failed to get allowed actions for user {actorId} on target {targetId}",
                ex
            );
        }
    }

    /// <summary>
    /// Checks if a user has authorization to perform an action on a target.
    /// </summary>
    /// <param name="actorId">The ID of the actor.</param>
    /// <param name="targetId">The ID of the target.</param>
    /// <param name="action">The action to check.</param>
    /// <param name="entityType">Optional: The specific type of entity being acted upon, for type-scoped permissions.</param>
    /// <returns>True if the user has authorization, otherwise false.</returns>
    public async Task<bool> IsAuthorizedAsync(string actorId, string targetId, string action, string? entityType = null)
    {
        // TODO: This method's internal logic needs to be updated to correctly use entityType,
        // or it should construct a ClaimsPrincipal and call the other IsAuthorizedAsync overload.
        // For now, only the signature is updated as per instructions.
        try
        {
            _logger.LogDebug(
                "Checking authorization for user {UserId} on target {TargetId} for action {Action}",
                actorId,
                targetId,
                action
            );

            var allowedActions = await GetAllowedActionsAsync(actorId, targetId);
            var isAuthorized = allowedActions.Any(aa => aa.Name == action);

            _logger.LogDebug("Authorization check result: {IsAuthorized}", isAuthorized);
            return isAuthorized;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error checking authorization for user {UserId} on target {TargetId} for action {Action}",
                actorId,
                targetId,
                action
            );
            // Default to not authorized in case of error
            _logger.LogWarning(
                "Authorization check failed, defaulting to not authorized for user {UserId} on target {TargetId} for action {Action}",
                actorId,
                targetId,
                action
            );
            return false;
        }
    }

    /// <summary>
    /// Checks if a user has authorization to perform an action on a target,
    /// considering hierarchical targets, overrides, and UserAuthActions with action hierarchy.
    /// </summary>
    /// <param name="actor">The claims principal representing the user.</param>
    /// <param name="targetId">The ID of the specific target resource.</param>
    /// <param name="action">The action to check.</param>
    /// <param name="entityType">Optional: The specific type of entity being acted upon, for type-scoped permissions.</param>
    /// <returns>True if the user has authorization, otherwise false.</returns>
    public async Task<bool> IsAuthorizedAsync(
        ClaimsPrincipal actor,
        string targetId,
        string action,
        string? entityType = null
    )
    {
        // The body of this method was already updated in the previous subtask.
        // This signature update ensures it matches the interface IAuthorizationProvider.
        // The entityType parameter will be used in the next refactoring step.
        if (actor == null)
        {
            _logger.LogWarning(
                "ClaimsPrincipal 'actor' is null during authorization check for target {TargetId} and action {Action}",
                targetId,
                action
            );
            return false;
        }

        // Step 1: HMFIC claim check (super admin)
        if (actor.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true"))
        {
            _logger.LogInformation(
                "[AUTH] User has HMFIC claim. Access GRANTED for Action: {Action} on Target: {TargetId}",
                action,
                targetId
            );
            return true;
        }

        var userId = actor.FindFirstValue(ClaimTypes.NameIdentifier);
        if (string.IsNullOrEmpty(userId))
        {
            _logger.LogWarning(
                "[AUTH] User ID not found in ClaimsPrincipal for Target: {TargetId}, Action: {Action}. Access DENIED.",
                targetId,
                action
            );
            return false;
        }

        _logger.LogDebug(
            "[AUTH] Check for User: {UserId}, Target: {TargetId}, Action: {Action}",
            userId,
            targetId,
            action
        );

        var currentTime = DateTimeOffset.UtcNow;

        // Step 2: Hierarchical Check (Overrides and UserAuthActions for specific target and its parents)
        var targetIdsToCheck = new List<string> { targetId };
        if (_targetHierarchyService != null) // Defensive check, though it should be injected
        {
            var parentTargetIds = await _targetHierarchyService.GetTargetHierarchyAsync(targetId);
            if (parentTargetIds != null)
            {
                targetIdsToCheck.AddRange(parentTargetIds);
            }
        }
        _logger.LogDebug(
            "[AUTH] Target hierarchy to check (specific to general): {TargetChain}",
            string.Join(" -> ", targetIdsToCheck)
        );

        foreach (var currentTargetIdInHierarchy in targetIdsToCheck)
        {
            _logger.LogDebug(
                "[AUTH] Checking current hierarchy level: Target: {CurrentTargetId}",
                currentTargetIdInHierarchy
            );

            // Step 2a: Override Check for currentTargetIdInHierarchy
            var overrides = (
                await _overridePermissionRepository.GetActiveByUserTargetAndActionAsync(
                    userId,
                    currentTargetIdInHierarchy,
                    action,
                    currentTime
                )
            ).ToList();
            if (overrides.Count > 0)
            {
                if (overrides.Any(o => !o.IsAllowed)) // Deny override
                {
                    _logger.LogInformation(
                        "[AUTH] Deny override found for User: {UserId}, Target: {CurrentTargetId}, Action: {Action}. Access DENIED.",
                        userId,
                        currentTargetIdInHierarchy,
                        action
                    );
                    return false;
                }
                if (overrides.Any(o => o.IsAllowed)) // Allow override
                {
                    _logger.LogInformation(
                        "[AUTH] Allow override found for User: {UserId}, Target: {CurrentTargetId}, Action: {Action}. Access GRANTED.",
                        userId,
                        currentTargetIdInHierarchy,
                        action
                    );
                    return true;
                }
            }

            // Step 2b: UserAuthAction Check for currentTargetIdInHierarchy
            var userAuthActions = await _userAuthActionRepository.GetByUserAndTargetAsync(
                userId,
                currentTargetIdInHierarchy
            );
            var directAuthActionsFromUAA = userAuthActions.Select(uaa => uaa.AuthAction).Where(a => a != null).ToList(); // No DistinctBy Name here yet, ExpandActionsAsync will handle uniqueness of names

            if (directAuthActionsFromUAA.Count > 0)
            {
                _logger.LogDebug(
                    "[AUTH] Found {Count} direct UserAuthActions for User: {UserId}, Target: {CurrentTargetId}. Expanding action hierarchy.",
                    directAuthActionsFromUAA.Count,
                    userId,
                    currentTargetIdInHierarchy
                );

                var loadedDirectAuthActions = new List<AuthAction>();
                var actionNames = directAuthActionsFromUAA.Select(a => a.Name).Distinct().ToList();
                if (actionNames.Count > 0) // Ensure we only query if there are names
                {
                    // Explicitly load AuthAction entities if not guaranteed by repository (as discussed)
                    loadedDirectAuthActions = await _context
                        .AuthActions.Where(a => actionNames.Contains(a.Name))
                        .ToListAsync();
                }

                var allowedActions = await ExpandActionsAsync(loadedDirectAuthActions);
                if (allowedActions.Any(aa => aa.Name == action))
                {
                    _logger.LogInformation(
                        "[AUTH] Action {Action} found in expanded UserAuthActions for User: {UserId}, Target: {CurrentTargetId}. Access GRANTED.",
                        action,
                        userId,
                        currentTargetIdInHierarchy
                    );
                    return true;
                }
            }
        }

        // Step 3: Universal Target "*" Check (Fallback)
        _logger.LogDebug(
            "[AUTH] No permission found in specific target hierarchy. Checking Universal Target '*' for User: {UserId}, Action: {Action}",
            userId,
            action
        );

        // Step 3a: Universal Override Check
        var universalOverrides = (
            await _overridePermissionRepository.GetActiveByUserTargetAndActionAsync(
                userId,
                AuthTypes.UNIVERSAL_TARGET,
                action,
                currentTime
            )
        ).ToList();
        if (universalOverrides.Count > 0)
        {
            if (universalOverrides.Any(o => !o.IsAllowed)) // Deny override
            {
                _logger.LogInformation(
                    "[AUTH] Universal Deny override found for User: {UserId}, Action: {Action}. Access DENIED.",
                    userId,
                    action
                );
                return false;
            }
            if (universalOverrides.Any(o => o.IsAllowed)) // Allow override
            {
                _logger.LogInformation(
                    "[AUTH] Universal Allow override found for User: {UserId}, Action: {Action}. Access GRANTED.",
                    userId,
                    action
                );
                return true;
            }
        }

        // Step 3b: Universal UserAuthAction Check
        var universalUserAuthActions = await _userAuthActionRepository.GetByUserAndTargetAsync(
            userId,
            AuthTypes.UNIVERSAL_TARGET
        );
        var directUniversalAuthActionsFromUAA = universalUserAuthActions
            .Select(uaa => uaa.AuthAction)
            .Where(a => a != null)
            .ToList();

        if (directUniversalAuthActionsFromUAA.Count > 0)
        {
            _logger.LogDebug(
                "[AUTH] Found {Count} direct Universal UserAuthActions for User: {UserId}. Expanding action hierarchy.",
                directUniversalAuthActionsFromUAA.Count,
                userId
            );

            var loadedDirectUniversalAuthActions = new List<AuthAction>();
            var universalActionNames = directUniversalAuthActionsFromUAA.Select(a => a.Name).Distinct().ToList();
            if (universalActionNames.Count > 0)
            {
                loadedDirectUniversalAuthActions = await _context
                    .AuthActions.Where(a => universalActionNames.Contains(a.Name))
                    .ToListAsync();
            }

            var allowedUniversalActions = await ExpandActionsAsync(loadedDirectUniversalAuthActions);
            if (allowedUniversalActions.Any(aa => aa.Name == action))
            {
                _logger.LogInformation(
                    "[AUTH] Action {Action} found in expanded Universal UserAuthActions for User: {UserId}. Access GRANTED.",
                    action,
                    userId
                );
                return true;
            }
        }

        // Step 4: Default Deny
        _logger.LogInformation(
            "[AUTH] No matching permissions found for User: {UserId}, Target: {TargetId}, Action: {Action} after all checks. Access DENIED.",
            userId,
            targetId,
            action
        );
        return false;
    }

    /// <summary>
    /// Updates an existing UserAuthAction.
    /// </summary>
    /// <param name="actorId">The ID of the user.</param>
    /// <param name="targetId">The ID of the target.</param>
    /// <param name="oldAction">The current action ID.</param>
    /// <param name="newAction">The new action name.</param>
    /// <returns>The updated UserAuthAction.</returns>
    public async Task<UserAuthAction?> UpdateUserAuthActionAsync(
        string actorId,
        string targetId,
        string oldAction,
        string newAction
    )
    {
        try
        {
            _logger.LogDebug(
                "Updating user auth action. UserId: {UserId}, TargetId: {TargetId}, OldAction: {OldAction}, NewAction: {NewAction}",
                actorId,
                targetId,
                oldAction,
                newAction
            );

            var userAuthAction = await _context.UserAuthActions.FirstOrDefaultAsync(uaa =>
                uaa.UserId == actorId && uaa.TargetId == targetId && uaa.AuthActionName == oldAction
            );
            if (userAuthAction == null)
            {
                _logger.LogWarning(
                    "UserAuthAction not found for update. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                    actorId,
                    targetId,
                    oldAction
                );
                throw new ArgumentException(
                    $"UserAuthAction not found for {actorId} on {targetId} with action {oldAction}"
                );
            }

            var authAction = await _context.AuthActions.FirstOrDefaultAsync(a => a.Name == newAction);
            if (authAction == null)
            {
                _logger.LogWarning("Invalid action name for update: {Action}", newAction);
                throw new ArgumentException($"Invalid action: {newAction}", nameof(newAction));
            }

            userAuthAction.AuthActionName = authAction.Name;

            _logger.LogInformation(
                "Updating UserAuthAction for user {UserId} on target {TargetId} from {OldAction} to {NewAction}",
                actorId,
                targetId,
                oldAction,
                newAction
            );

            _context.UserAuthActions.Update(userAuthAction);
            await _context.SaveChangesAsync();
            _logger.LogInformation("UserAuthAction updated successfully");
            return userAuthAction;
        }
        catch (ArgumentException)
        {
            // Already logged above
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(
                ex,
                "Database error while updating user auth action. UserId: {UserId}, TargetId: {TargetId}, OldAction: {OldAction}, NewAction: {NewAction}",
                actorId,
                targetId,
                oldAction,
                newAction
            );
            throw new InvalidOperationException(
                $"Failed to update authorization for user {actorId} on target {targetId} from {oldAction} to {newAction}",
                ex
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unexpected error while updating user auth action. UserId: {UserId}, TargetId: {TargetId}, OldAction: {OldAction}, NewAction: {NewAction}",
                actorId,
                targetId,
                oldAction,
                newAction
            );
            throw new InvalidOperationException(
                $"Failed to update authorization for user {actorId} on target {targetId} from {oldAction} to {newAction}",
                ex
            );
        }
    }

    public virtual async Task<AuthAction?> GetActionAsync(string actorId, string targetId, string actionName)
    {
        try
        {
            _logger.LogDebug(
                "Getting specific action. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                actionName
            );

            var userAuthAction = await _context.UserAuthActions.FindAsync(actorId, targetId, actionName);

            if (userAuthAction == null)
            {
                _logger.LogDebug(
                    "No UserAuthAction found for user {UserId}, target {TargetId}, action {Action}",
                    actorId,
                    targetId,
                    actionName
                );
            }

            return userAuthAction?.AuthAction;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error getting specific action for user {UserId} on target {TargetId} with action {Action}",
                actorId,
                targetId,
                actionName
            );
            throw new InvalidOperationException(
                $"Failed to get action for user {actorId} on target {targetId} with action {actionName}",
                ex
            );
        }
    }

    public virtual async Task<TargetType?> GetTargetTypeAsync(string id)
    {
        try
        {
            _logger.LogDebug("Getting target type for ID: {Id}", id);

            var matchingTargets = await _context
                .TargetTypes.Where(tt => id.StartsWith(tt.IdPrefix))
                .OrderByDescending(tt => tt.IdPrefix.Length)
                .ToListAsync();

            var result = matchingTargets.Count != 0 ? matchingTargets.FirstOrDefault() : null;

            if (result == null)
            {
                _logger.LogDebug("No target type found for ID: {Id}", id);
            }
            else
            {
                _logger.LogDebug("Found target type {Name} for ID: {Id}", result.Name, id);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting target type for ID: {Id}", id);
            throw new InvalidOperationException($"Failed to get target type for ID {id}", ex);
        }
    }

    /// <summary>
    /// Expands the list of actions to include all actions implied by the hierarchy.
    /// </summary>
    /// <param name="authActions">The list of actions to expand.</param>
    /// <returns>A list of actions including all implied actions.</returns>
    protected internal async Task<List<AuthAction>> ExpandActionsAsync(List<AuthAction> authActions)
    {
        _logger.LogDebug("Expanding {Count} actions based on hierarchy", authActions.Count);
        try
        {
            var expandedActions = new HashSet<string>(authActions.Select(aa => aa.Name));
            _logger.LogDebug("Starting with {Count} unique actions", expandedActions.Count);

            // Fetch all action hierarchies in a single query
            var actionHierarchies = await _context.ActionHierarchies.ToListAsync();
            _logger.LogDebug("Loaded {Count} action hierarchy relationships", actionHierarchies.Count);

            // Create a dictionary to map parent actions to their child actions
            var hierarchyDict = actionHierarchies
                .GroupBy(ah => ah.ParentActionName)
                .ToDictionary(g => g.Key, g => g.Select(ah => ah.ChildActionName).ToList());

            // Process the hierarchy in memory
            var actionQueue = new Queue<string>(authActions.Select(aa => aa.Name));
            while (actionQueue.Count > 0)
            {
                var currentAction = actionQueue.Dequeue();
                if (hierarchyDict.TryGetValue(currentAction, out var childActions) && childActions != null)
                {
                    var newChildActions = 0;
                    foreach (
                        var childAction in from childAction in childActions
                        where expandedActions.Add(childAction)
                        select childAction
                    )
                    {
                        actionQueue.Enqueue(childAction);
                        newChildActions++;
                    }
                    _logger.LogDebug(
                        "Action {Action} implied {Count} new child actions",
                        currentAction,
                        newChildActions
                    );
                }
            }

            var result = await _context.AuthActions.Where(aa => expandedActions.Contains(aa.Name)).ToListAsync();
            _logger.LogInformation("Expanded to {Count} total actions", result.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Problem in ExpandActionsAsync");
            throw;
        }
    }

    private async Task ValidateActionsAsync(IEnumerable<string> actions)
    {
        _logger.LogDebug("Validating {Count} actions", actions.Count());

        var invalidActions = new List<string>();
        foreach (var action in actions.Distinct())
        {
            if (await _context.AuthActions.FirstOrDefaultAsync(a => a.Name == action) == null)
            {
                _logger.LogWarning("Invalid action name: {Action}", action);
                invalidActions.Add(action);
            }
        }
        if (invalidActions.Count != 0)
        {
            _logger.LogWarning(
                "Found {Count} invalid actions: {Actions}",
                invalidActions.Count,
                string.Join(", ", invalidActions)
            );
            throw new InvalidOperationException($"Invalid actions {string.Join(", ", invalidActions)}");
        }

        _logger.LogDebug("All actions validated successfully");
    }

    private async Task ValidateAuthActionAsync(string action)
    {
        _logger.LogDebug("Validating action: {Action}", action);

        var authAction = await _context.AuthActions.FindAsync(action);
        if (authAction == null)
        {
            _logger.LogWarning("Action not found: {Action}", action);
            throw new InvalidOperationException("The specified action does not exist.");
        }

        _logger.LogDebug("Action validated successfully");
    }

    private async Task ValidateTargetIdAsync(string targetId)
    {
        _logger.LogDebug("Validating target ID: {TargetId}", targetId);

        if (await GetTargetTypeAsync(targetId) == null)
        {
            _logger.LogWarning("Target not found: {TargetId}", targetId);
            throw new InvalidOperationException($"The specified target {targetId} does not exist.");
        }

        _logger.LogDebug("Target validated successfully");
    }

    private async Task ValidateTargetIdsAsync(IEnumerable<string> targetIds)
    {
        _logger.LogDebug("Validating {Count} target IDs", targetIds.Count());

        var invalidTargets = new List<string>();
        foreach (var targetId in targetIds.Distinct())
        {
            if (await GetTargetTypeAsync(targetId) == null)
            {
                _logger.LogWarning("Invalid target ID: {TargetId}", targetId);
                invalidTargets.Add(targetId);
            }
        }
        if (invalidTargets.Count != 0)
        {
            _logger.LogWarning(
                "Found {Count} invalid targets: {Targets}",
                invalidTargets.Count,
                string.Join(", ", invalidTargets)
            );
            throw new InvalidOperationException($"Invalid targets {string.Join(", ", invalidTargets)}");
        }

        _logger.LogDebug("All targets validated successfully");
    }

    private async Task ValidateUserAuthActionExistsAsync(string userId, IEnumerable<string> targetIds, string action)
    {
        _logger.LogDebug(
            "Validating user auth actions don't already exist. UserId: {UserId}, Action: {Action}",
            userId,
            action
        );

        foreach (var targetId in targetIds)
        {
            // Check if this authorization already exists
            if ((await GetActionAsync(userId, targetId, action)) != null)
            {
                _logger.LogWarning(
                    "UserAuthAction already exists. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                    userId,
                    targetId,
                    action
                );
                throw new InvalidOperationException("One or more of these UserAuthAction already exists for this user");
            }
        }

        _logger.LogDebug("All user auth actions validated as new");
    }

    private async Task ValidateUserAuthActionIsNewAsync(string actorId, string targetId, string action)
    {
        _logger.LogDebug(
            "Validating user auth action is new. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
            actorId,
            targetId,
            action
        );

        if ((await GetActionAsync(actorId, targetId, action)) != null)
        {
            _logger.LogWarning(
                "UserAuthAction already exists. UserId: {UserId}, TargetId: {TargetId}, Action: {Action}",
                actorId,
                targetId,
                action
            );
            throw new InvalidOperationException(
                "UserAuthAction already exists for this user with the given target and action."
            );
        }

        _logger.LogDebug("User auth action confirmed as new");
    }

    private async Task ValidateUserIdAsync(string userId)
    {
        _logger.LogDebug("Validating user ID: {UserId}", userId);

        var user = await _context.Users.FindAsync(userId);
        if (user == null)
        {
            _logger.LogWarning("User not found: {UserId}", userId);
            throw new InvalidOperationException("The specified user does not exist.");
        }

        _logger.LogDebug("User validated successfully");
    }

    private async Task ValidateUserIdsAsync(IEnumerable<string> userIds)
    {
        try
        {
            _logger.LogDebug("Validating {Count} user IDs", userIds.Count());

            var invalidUserIds = new List<string>();
            foreach (var userId in userIds.Distinct())
            {
                if (await _context.Users.FindAsync(userId) == null)
                {
                    _logger.LogWarning("Invalid user ID: {UserId}", userId);
                    invalidUserIds.Add(userId);
                }
            }
            if (invalidUserIds.Count != 0)
            {
                _logger.LogWarning(
                    "Found {Count} invalid users: {Users}",
                    invalidUserIds.Count,
                    string.Join(", ", invalidUserIds)
                );
                throw new InvalidOperationException($"Users do not exist: {string.Join(", ", invalidUserIds)}");
            }

            _logger.LogDebug("All users validated successfully");
        }
        catch (Exception ex) when (ex is not InvalidOperationException)
        {
            _logger.LogError(ex, "Unexpected error validating user IDs");
            throw new InvalidOperationException("Failed to validate user IDs", ex);
        }
    }

    #endregion
}
