@page "/organizingAuthorities/create"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Authorization
@using ProScoring.BusinessLogic.ServiceInterfaces
@using ProScoring.Domain.Dtos
@using ProScoring.Domain.Entities
@using ProScoring.Infrastructure.Authorization
@using ProScoring.Blazor.Extensions
@using Radzen
@using Radzen.Blazor
@inject IOrganizingAuthorityService organizingAuthorityService
@inject NavigationManager navigationManager
@inject TooltipService tooltipService
@inject ILogger<Create> logger
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Create Organizing Authority</PageTitle>

<div class="d-flex justify-content-center align-items-start" style="min-height: 100vh; padding: 2rem;">
    <RadzenCard Style="width: 100%; max-width: 800px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 8px;">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="mb-0">Create</h1>
                <h2 class="mb-0">Organizing Authority</h2>
            </div>
        </div>

        <RadzenTemplateForm TItem="OrganizingAuthorityUploadDto" Data="@dto" Submit="@AddOrganizingAuthority">
            <RadzenFieldset Text="Details">
                <RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem">
                    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center"
                        JustifyContent="JustifyContent.End">
                        <RadzenLabel Text="Private" Component="Private" />
                        <RadzenSwitch @bind-Value=@dto.Private Name="Private" data-testid="oa-private-checkbox" />
                        <RadzenIcon Icon="info" Style="cursor: pointer;"
                            MouseEnter="@(args => ShowTooltip(args,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                "Private OAs are not discoverable by search.",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      new TooltipOptions { Delay = 500, Position = TooltipPosition.Left }))" />
                    </RadzenStack>
                    <RadzenFormField Text="Club (or OA) Name">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.Name" Name="Name" Placeholder="Name" Style="width: 100%;"
                                @onblur="StateHasChanged" data-testid="oa-name-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="Name" Text="Name is required"
                                Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Website">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.Website" Name="Website" Placeholder="Website"
                                Style="width: 100%" data-testid="oa-website-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="Website" Text="Website is required"
                                Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Email">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.Email" Name="Email" Placeholder="Email" Style="width: 100%;"
                                data-testid="oa-email-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="Email" Text="Email is required"
                                Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Phone">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.Phone" Name="Phone" Placeholder="Phone" Style="width: 100%;"
                                data-testid="oa-phone-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="Phone" Text="Phone is required"
                                Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Address Line 1">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.AddressLine1" Name="AddressLine1"
                                Placeholder="Address Line 1" Style="width: 100%;" data-testid="oa-address1-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="AddressLine1" Text="Address Line 1 is required"
                                Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Address Line 2">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.AddressLine2" Name="AddressLine2"
                                Placeholder="Address Line 2" Style="width: 100%;" data-testid="oa-address2-input" />
                        </ChildContent>
                    </RadzenFormField>
                    <RadzenFormField Text="City">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.City" Name="City" Placeholder="City" Style="width: 100%;"
                                data-testid="oa-city-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="City" Text="City is required"
                                Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="State">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.State" Name="State" Placeholder="State" Style="width: 100%;"
                                data-testid="oa-state-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenCustomValidator Component="State" Text="State is required for US addresses"
                                Style="position: absolute;"
                                Validator="@(() => ValidateStateForUSCountry(dto?.State))" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Postal Code">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.PostalCode" Name="PostalCode" Placeholder="Postal Code"
                                Style="width: 100%;" data-testid="oa-postal-code-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="PostalCode" Text="Postal Code is required"
                                Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Country">
                        <ChildContent>
                            <RadzenTextBox @bind-Value="dto.Country" Name="Country" Placeholder="Country"
                                Style="width: 100%;" data-testid="oa-country-input" />
                        </ChildContent>
                        <Helper>
                            <RadzenRequiredValidator Component="Country" Text="Country is required"
                                Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenFormField Text="Description">
                        <ChildContent>
                            <RadzenTextArea @bind-Value="dto.Description" Name="Description" Placeholder="Description"
                                Style="width: 100%; min-height: 100px;" MaxLength="1000"
                                @attributes="@("oa-description-input".AsTestId())" />
                        </ChildContent>
                        <Helper>
                            <RadzenLengthValidator Component="Description" Max="1000"
                                Text="Description cannot exceed 1000 characters" Style="position: absolute;" />
                        </Helper>
                    </RadzenFormField>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Center">
                        <RadzenCard class="rz-m-0 rz-m-md-1" Style="width: 100%; max-width: 400px;">
                            <RadzenText TextStyle="TextStyle.H4" class="text-center">
                                Select
                                @if (!string.IsNullOrWhiteSpace(dto.Name))
                                {
                                    @dto.Name
                                }
                                Burgee file
                            </RadzenText>
                            <RadzenRow class="justify-content-center">
                                <RadzenColumn Size="12">
                                    <RadzenFileInput @bind-Value=@dto.BurgeeDataUri @bind-FileSize=@fileSize
                                        @bind-FileName=@dto.BurgeeFileName MaxFileSize="1048576" TValue="string"
                                        Style="width: 100%" Error=@(args => OnError(args, "FileInput"))
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "select burgee file" }, { "data-testid", "oa-burgee-input" } })" />
                                </RadzenColumn>
                            </RadzenRow>
                            <RadzenText class="text-center" style="color: var(--rz-text-tertiary-color);">
                                (optional)
                            </RadzenText>
                        </RadzenCard>
                    </RadzenStack>
                </RadzenStack>
            </RadzenFieldset>

            <RadzenAlert AlertStyle="AlertStyle.Info" ShowIcon="true" ShowCloseButton="false" class="mt-4"
                data-testid="oa-approval-note">
                <strong>Note:</strong> Organizing Authorities are not enabled until approved by an administrator.
            </RadzenAlert>

            <div class="d-flex justify-content-between align-items-center gap-3 mt-4">
                <div>
                    @if (IsHmfic)
                    {
                        <RadzenButton Text="List" ButtonStyle="ButtonStyle.Light" Click="@GoToList" Class="ml-2"
                            @attributes="@("oa-list-button".AsTestId())" />
                    }
                </div>
                <div class="gap-3">
                    <RadzenButton Text="Back" ButtonStyle="ButtonStyle.Light" Click="@GoBack"
                        @attributes="@("oa-back-button".AsTestId())" />
                    <RadzenButton Text="Create" ButtonType="ButtonType.Submit" ButtonStyle="ButtonStyle.Primary"
                        @attributes="@("create-oa-button".AsTestId())" />
                </div>
            </div>
        </RadzenTemplateForm>
    </RadzenCard>
</div>

@code {
    [SupplyParameterFromForm]
    OrganizingAuthorityUploadDto dto { get; set; } = new();
    private long? fileSize;
    private bool IsHmfic { get; set; }

    /// <summary>
    /// Initializes the component and checks if the user has HMFIC rights.
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        // Check if the user has HMFIC rights
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        IsHmfic = authState.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true");
    }

    /// <summary>
    /// Creates a new organizing authority from the form data.
    /// </summary>
    /// <param name="organizingAuthority">The organizing authority data from the form.</param>
    private async Task AddOrganizingAuthority(OrganizingAuthorityUploadDto organizingAuthority)
    {
        await organizingAuthorityService.CreateAsync(organizingAuthority);
        navigationManager.NavigateTo("/organizingAuthorities");
    }

    /// <summary>
    /// Shows a tooltip at the specified element.
    /// </summary>
    /// <param name="elementReference">The element to attach the tooltip to.</param>
    /// <param name="text">The tooltip text.</param>
    /// <param name="options">Optional tooltip configuration options.</param>
    private void ShowTooltip(ElementReference elementReference, string text, TooltipOptions? options = null)
    {
        tooltipService.Open(elementReference, text, options);
    }

    /// <summary>
    /// Handles invalid form submissions.
    /// </summary>
    /// <param name="organizingAuthority">The organizing authority with invalid data.</param>
    private void OnInvalidSubmit(OrganizingAuthority organizingAuthority)
    {
        // Method intentionally left empty
    }

    /// <summary>
    /// Validates that State is provided when Country is a US variation.
    /// </summary>
    /// <param name="value">The state value being validated.</param>
    /// <returns>True if valid, false otherwise.</returns>
    private bool ValidateStateForUSCountry(string? value)
    {
        string[] usCountryVariations = { "United States", "US", "USA" };

        // If country is any US variation, state is required (case-insensitive comparison)
        if (dto.Country != null)
        {
            string countryTrimmed = dto.Country.Trim();
            bool isUSCountry = usCountryVariations.Any(v =>
            string.Equals(v, countryTrimmed, StringComparison.OrdinalIgnoreCase));

            if (isUSCountry)
            {
                return !string.IsNullOrWhiteSpace(dto.State);
            }
        }

        // State not required for non-US countries
        return true;
    }

    /// <summary>
    /// Handles file upload errors.
    /// </summary>
    /// <param name="args">The error event arguments.</param>
    /// <param name="name">The name of the component that raised the error.</param>
    private void OnError(UploadErrorEventArgs args, string name)
    {
        Console.WriteLine($"{args.Message}");
    }

    /// <summary>
    /// Navigates back to the previous page.
    /// </summary>
    private void GoBack()
    {
        navigationManager.NavigateTo("javascript:window.history.back()");
    }
    /// <summary>
    /// Navigates to the organizing authorities list page.
    /// </summary>
    private void GoToList()
    {
        navigationManager.NavigateTo("/organizingAuthorities");
    }
}
