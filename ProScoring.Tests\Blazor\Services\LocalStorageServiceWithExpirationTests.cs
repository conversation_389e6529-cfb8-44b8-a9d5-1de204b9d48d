using System.Text.Json;
using Blazored.LocalStorage;
using FluentAssertions;
using Microsoft.Extensions.Options;
using NSubstitute;
using ProScoring.Blazor.Options;
using ProScoring.Blazor.Services;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Tests.Blazor.Services;

/// <summary>
/// Tests for the <see cref="LocalStorageServiceWithExpiration"/> class which provides local storage functionality with expiration support.
/// </summary>
public class LocalStorageServiceWithExpirationTests
{
    #region Fields
    private readonly ILocalStorageService _localStorageSubstitute;
    private readonly IDateTimeOffsetProvider _dateTimeProviderSubstitute;
    private readonly IOptions<LocalStorageOptions> _optionsSubstitute;
    private readonly LocalStorageServiceWithExpiration _service;
    private readonly DateTimeOffset _fakeUtcNow = new(2023, 1, 1, 12, 0, 0, TimeSpan.Zero);
    private readonly int _defaultExpirationMinutes = 30;
    #endregion Fields

    #region Test Data
    /// <summary>
    /// Simple test data class used for serialization/deserialization tests.
    /// </summary>
    private sealed class TestData
    {
        public string? Name { get; set; }

        public override bool Equals(object? obj) => obj is TestData data && Name == data.Name;

        public override int GetHashCode() => Name?.GetHashCode() ?? 0;
    }
    #endregion Test Data

    #region Constructor
    public LocalStorageServiceWithExpirationTests()
    {
        _localStorageSubstitute = Substitute.For<ILocalStorageService>();
        _dateTimeProviderSubstitute = Substitute.For<IDateTimeOffsetProvider>();
        _dateTimeProviderSubstitute.UtcNow.Returns(_fakeUtcNow);

        _optionsSubstitute = Substitute.For<IOptions<LocalStorageOptions>>();
        _optionsSubstitute.Value.Returns(
            new LocalStorageOptions { ExpirationTimeoutMinutes = _defaultExpirationMinutes }
        );

        _service = new LocalStorageServiceWithExpiration(
            _localStorageSubstitute,
            _optionsSubstitute,
            _dateTimeProviderSubstitute
        );
    }
    #endregion Constructor

    #region Helper Methods
    /// <summary>
    /// Validates the JSON representation of a stored item against expected values.
    /// </summary>
    /// <typeparam name="T">The type of the stored value.</typeparam>
    /// <param name="json">The JSON string to validate.</param>
    /// <param name="expectedValue">The expected value stored in the JSON.</param>
    /// <param name="expectedExpiration">The expected expiration time.</param>
    /// <param name="expectedLifetime">The expected lifetime duration.</param>
    /// <returns>True if the JSON matches all expected values, false otherwise.</returns>
    private bool AssertStoredJson<T>(
        string json,
        T expectedValue,
        DateTimeOffset expectedExpiration,
        TimeSpan? expectedLifetime
    )
    {
        var deserialized = JsonSerializer.Deserialize<LocalSettingWithExpiration<T>>(json);
        deserialized.Should().NotBeNull();
        if (deserialized == null)
            return false;

        deserialized.Value.Should().BeEquivalentTo(expectedValue);
        deserialized.Expiration.Should().BeCloseTo(expectedExpiration, TimeSpan.FromSeconds(1));
        deserialized.LifeTime.Should().Be(expectedLifetime);
        return true;
    }
    #endregion Helper Methods

    #region SetItemAsync Tests
    /// <summary>
    /// Tests that SetItemAsync correctly stores a value with a specified lifetime.
    /// </summary>
    [Fact]
    public async Task SetItemAsync_WithLifetime_StoresCorrectValueAndExpiration()
    {
        // Arrange
        string key = "testKey";
        string value = "testValue";
        TimeSpan lifetime = TimeSpan.FromMinutes(10);

        // Act
        await _service.SetItemAsync(key, value, lifetime);

        // Assert
        await _localStorageSubstitute
            .Received(1)
            .SetItemAsStringAsync(
                key,
                Arg.Is<string>(json => AssertStoredJson(json, value, _fakeUtcNow.Add(lifetime), lifetime))
            );
    }

    /// <summary>
    /// Tests that SetItemAsync uses the default expiration when lifetime is null.
    /// </summary>
    [Fact]
    public async Task SetItemAsync_NullLifetime_UsesDefaultExpiration()
    {
        // Arrange
        string key = "testKey";
        string value = "testValue";

        // Act
        await _service.SetItemAsync(key, value, null);

        // Assert
        await _localStorageSubstitute
            .Received(1)
            .SetItemAsStringAsync(
                key,
                Arg.Is<string>(json =>
                    AssertStoredJson(json, value, _fakeUtcNow.AddMinutes(_defaultExpirationMinutes), null)
                )
            );
    }

    /// <summary>
    /// Tests that SetItemAsync handles local storage errors gracefully.
    /// </summary>
    [Fact]
    public async Task SetItemAsync_LocalStorageThrows_DoesNotThrow()
    {
        // Arrange
        _localStorageSubstitute
            .SetItemAsStringAsync(Arg.Any<string>(), Arg.Any<string>())
            .Returns(ValueTask.FromException(new Exception("Storage error")));

        // Act
        Func<Task> act = async () => await _service.SetItemAsync("key", "value", null);

        // Assert
        await act.Should().NotThrowAsync();
    }
    #endregion SetItemAsync Tests

    #region GetItemAsync Tests
    /// <summary>
    /// Tests that GetItemAsync returns null when item doesn't exist.
    /// </summary>
    [Fact]
    public async Task GetItemAsync_ItemDoesNotExist_ReturnsDefault()
    {
        // Arrange
        _localStorageSubstitute.GetItemAsStringAsync("key").Returns(ValueTask.FromResult<string?>(null));

        // Act
        var result = await _service.GetItemAsync<string>("key");

        // Assert
        result.Should().BeNull();
    }

    /// <summary>
    /// Tests that GetItemAsync returns value and refreshes expiration by default.
    /// </summary>
    [Fact]
    public async Task GetItemAsync_ItemNotExpired_ReturnsValueAndRefreshesExpirationByDefault()
    {
        // Arrange
        string key = "key";
        var originalValue = new TestData { Name = "Test" };
        TimeSpan originalLifetime = TimeSpan.FromMinutes(60);
        DateTimeOffset futureExpiration = _fakeUtcNow.AddMinutes(30);
        var setting = new LocalSettingWithExpiration<TestData>
        {
            Value = originalValue,
            Expiration = futureExpiration,
            LifeTime = originalLifetime,
        };
        _localStorageSubstitute
            .GetItemAsStringAsync(key)
            .Returns(ValueTask.FromResult<string?>(JsonSerializer.Serialize(setting)));

        // Act
        var result = await _service.GetItemAsync<TestData>(key);

        // Assert
        result.Should().BeEquivalentTo(originalValue);
        await _localStorageSubstitute
            .Received(1)
            .SetItemAsStringAsync(
                key,
                Arg.Is<string>(json =>
                    AssertStoredJson(json, originalValue, _fakeUtcNow.Add(originalLifetime), originalLifetime)
                )
            );
    }

    /// <summary>
    /// Tests that GetItemAsync does not refresh expiration when refreshExpiration is false.
    /// </summary>
    [Fact]
    public async Task GetItemAsync_ItemNotExpired_NoRefresh_ReturnsValueAndDoesNotRefresh()
    {
        // Arrange
        string key = "key";
        var originalValue = new TestData { Name = "Test" };
        TimeSpan originalLifetime = TimeSpan.FromMinutes(60);
        DateTimeOffset futureExpiration = _fakeUtcNow.AddMinutes(30);
        var setting = new LocalSettingWithExpiration<TestData>
        {
            Value = originalValue,
            Expiration = futureExpiration,
            LifeTime = originalLifetime,
        };
        _localStorageSubstitute
            .GetItemAsStringAsync(key)
            .Returns(ValueTask.FromResult<string?>(JsonSerializer.Serialize(setting)));

        // Act
        var result = await _service.GetItemAsync<TestData>(key, refreshExpiration: false);

        // Assert
        result.Should().BeEquivalentTo(originalValue);
        await _localStorageSubstitute.DidNotReceive().SetItemAsStringAsync(Arg.Any<string>(), Arg.Any<string>());
    }

    /// <summary>
    /// Tests that GetItemAsync removes expired items and returns null.
    /// </summary>
    [Fact]
    public async Task GetItemAsync_ItemExpired_RemovesItemAndReturnsDefault()
    {
        // Arrange
        string key = "key";
        DateTimeOffset pastExpiration = _fakeUtcNow.AddMinutes(-10);
        var setting = new LocalSettingWithExpiration<string> { Value = "expiredData", Expiration = pastExpiration };
        _localStorageSubstitute
            .GetItemAsStringAsync(key)
            .Returns(ValueTask.FromResult<string?>(JsonSerializer.Serialize(setting)));

        // Act
        var result = await _service.GetItemAsync<string>(key);

        // Assert
        result.Should().BeNull();
        await _localStorageSubstitute.Received(1).RemoveItemAsync(key);
    }

    /// <summary>
    /// Tests that GetItemAsync handles local storage errors during get operation
    /// </summary>
    [Fact]
    public async Task GetItemAsync_LocalStorageThrowsOnGet_ReturnsDefault()
    {
        // Arrange
        _localStorageSubstitute
            .GetItemAsStringAsync(Arg.Any<string>())
            .Returns(ValueTask.FromException<string?>(new Exception("Storage error")));

        // Act
        var result = await _service.GetItemAsync<string>("key");

        // Assert
        result.Should().BeNull();
    }

    /// <summary>
    /// Tests that GetItemAsync handles local storage errors during remove operation
    /// </summary>
    [Fact]
    public async Task GetItemAsync_LocalStorageThrowsOnRemove_ReturnsDefaultAndDoesNotThrow()
    {
        // Arrange
        string key = "key";
        DateTimeOffset pastExpiration = _fakeUtcNow.AddMinutes(-10);
        var setting = new LocalSettingWithExpiration<string> { Value = "expiredData", Expiration = pastExpiration };
        _localStorageSubstitute
            .GetItemAsStringAsync(key)
            .Returns(ValueTask.FromResult<string?>(JsonSerializer.Serialize(setting)));
        _localStorageSubstitute
            .RemoveItemAsync(Arg.Any<string>())
            .Returns(ValueTask.FromException(new Exception("Storage error")));

        // Act
        var result = await _service.GetItemAsync<string>(key);

        // Assert
        result.Should().BeNull();
    }
    #endregion GetItemAsync Tests

    #region RemoveItemAsync Tests
    /// <summary>
    /// Tests that RemoveItemAsync calls the underlying local storage service
    /// </summary>
    [Fact]
    public async Task RemoveItemAsync_CallsLocalStorageRemove()
    {
        // Arrange
        string key = "testKey";

        // Act
        await _service.RemoveItemAsync(key);

        // Assert
        await _localStorageSubstitute.Received(1).RemoveItemAsync(key);
    }

    /// <summary>
    /// Tests that RemoveItemAsync handles local storage errors gracefully
    /// </summary>
    [Fact]
    public async Task RemoveItemAsync_LocalStorageThrows_DoesNotThrow()
    {
        // Arrange
        _localStorageSubstitute
            .RemoveItemAsync(Arg.Any<string>())
            .Returns(ValueTask.FromException(new Exception("Storage error")));

        // Act
        Func<Task> act = async () => await _service.RemoveItemAsync("key");

        // Assert
        await act.Should().NotThrowAsync();
    }
    #endregion RemoveItemAsync Tests
}
