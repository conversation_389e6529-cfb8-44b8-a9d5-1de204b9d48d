using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;

namespace ProScoring.Infrastructure.Extensions;

public static class ServiceConfigurationExtensions
{
    #region methods

    public static IHostApplicationBuilder AddInfrastructureServices(this IHostApplicationBuilder builder)
    {
        var dbOptions =
            builder.Configuration.GetSection(DatabaseOptions.SECTION_NAME).Get<DatabaseOptions>()
            ?? throw new InvalidOperationException("Database options not found.");

        var appConfigSection = builder.Configuration.GetSection(FileUploadOptions.SECTION_NAME);
        // Bind FileUploadOptions from configuration
        builder.Services.Configure<FileUploadOptions>(builder.Configuration.GetSection(FileUploadOptions.SECTION_NAME));

        // Check if the application is in the process of creating a migration.
        // If it is, then we configure both databases, as we are not actually running the application.
        // The environment variable is set in the batch file used for creating the migrations.
        if (builder.Configuration.GetValue<bool>("CREATING_MIGRATION"))
        {
            return builder
                .ConfigureSqlite<SqliteCreationApplicationDbContext>()
                .ConfigurePostgreSql<PostgreSqlCreationApplicationDbContext>();
        }
        else if (dbOptions.UseSqLite)
        {
            builder.ConfigureSqlite<ApplicationDbContext>();
        }
        else
        {
            builder.ConfigurePostgreSql<ApplicationDbContext>();
        }
        builder.Services.AddDatabaseDeveloperPageExceptionFilter();

        //builder.Services.AddScoped<AuthorizationService>();
        builder.Services.AddScoped<IAuthorizationProvider, ProScoringAuthorizationService>();
        builder.Services.AddScoped<IProScoringAuthorizationService, ProScoringAuthorizationService>();
        builder.Services.AddScoped<IApplicationDbContext, ApplicationDbContext>();

        // setup time provider
        builder.Services.AddSingleton<IDateTimeOffsetProvider, DateTimeOffsetProviderCpuTime>();

        // setup email sender service
        builder.Services.AddTransient<IEmailSender, PostmarkEmailSenderService>();
        builder.Services.AddTransient<IEmailSender<ApplicationUser>, EmailSenderService>();

        builder.Services.AddSingleton<IIdGenerationUtilService, GuidishIdGenerationUtilService>();
        builder.Services.AddSingleton<IValueGenerator, CustomIdValueGenerator>();

        builder.Services.AddScoped<IFileService, FileService>();

        // Register Authorization Repositories
        builder.Services.AddScoped<
            ProScoring.Infrastructure.Authorization.Repositories.Interfaces.IUserAuthActionRepository,
            ProScoring.Infrastructure.Authorization.Repositories.UserAuthActionRepository
        >();
        builder.Services.AddScoped<
            ProScoring.Infrastructure.Authorization.Repositories.Interfaces.IOverridePermissionRepository,
            ProScoring.Infrastructure.Authorization.Repositories.OverridePermissionRepository
        >();

        // Register Target Hierarchy Service
        builder.Services.AddScoped<
            ITargetHierarchyService,
            ProScoring.Infrastructure.Services.TargetHierarchyService
        >();

        return builder;
    }

    private static IHostApplicationBuilder ConfigurePostgreSql<T>(this IHostApplicationBuilder builder)
        where T : IdentityDbContext<ApplicationUser>
    {
        var connectionString =
            builder.Configuration.GetConnectionString("PostgreSqlConnection")
            ?? throw new InvalidOperationException("Connection string 'PostgreSqlConnection' not found.");
        builder.Services.AddDbContext<T>(options => options.UseNpgsql(connectionString), ServiceLifetime.Scoped);
        builder.Services.AddDbContextFactory<T>(
            options => options.UseNpgsql(connectionString),
            lifetime: ServiceLifetime.Scoped
        );
        builder.EnrichNpgsqlDbContext<T>(); // add support for Npgsql-specific EF Core features

        builder
            .Services.AddIdentityCore<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
            .AddEntityFrameworkStores<T>()
            .AddSignInManager()
            .AddDefaultTokenProviders();

        return builder;
    }

    private static IHostApplicationBuilder ConfigureSqlite<T>(this IHostApplicationBuilder builder)
        where T : IdentityDbContext<ApplicationUser>
    {
        builder.Services.AddDbContextFactory<T>(
            options =>
            {
                var connectionString =
                    builder.Configuration.GetConnectionString("SQLiteConnection")
                    ?? throw new InvalidOperationException("Connection string 'SQLiteConnection' not found.");
                options.UseSqlite(connectionString);
            },
            ServiceLifetime.Scoped
        );
        builder.Services.AddDatabaseDeveloperPageExceptionFilter();

        builder
            .Services.AddIdentityCore<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
            .AddEntityFrameworkStores<T>()
            .AddSignInManager()
            .AddDefaultTokenProviders();

        return builder;
    }

    #endregion
}
