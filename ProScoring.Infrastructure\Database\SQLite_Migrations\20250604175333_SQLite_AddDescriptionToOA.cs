﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProScoring.Infrastructure.Database.SQLite_Migrations
{
    /// <inheritdoc />
    public partial class SQLite_AddDescriptionToOA : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RegattaExternalLinks_LinkType_LinkTypeId",
                table: "RegattaExternalLinks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LinkType",
                table: "LinkType");

            migrationBuilder.RenameTable(
                name: "LinkType",
                newName: "LinkTypes");

            migrationBuilder.AddColumn<string>(
                name: "ScopedEntityType",
                table: "UserAuthActions",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "Regattas",
                type: "TEXT",
                maxLength: 60,
                nullable: false,
                defaultValue: "")
                .Annotation("Relational:ColumnOrder", 167);

            migrationBuilder.AddColumn<decimal>(
                name: "EntryFee",
                table: "Regattas",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m)
                .Annotation("Relational:ColumnOrder", 175);

            migrationBuilder.AddColumn<decimal>(
                name: "LateFee",
                table: "Regattas",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m)
                .Annotation("Relational:ColumnOrder", 180);

            migrationBuilder.AddColumn<DateOnly>(
                name: "LateFeeDate",
                table: "Regattas",
                type: "TEXT",
                nullable: true)
                .Annotation("Relational:ColumnOrder", 185);

            migrationBuilder.AddColumn<string>(
                name: "PostalCode",
                table: "Regattas",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "")
                .Annotation("Relational:ColumnOrder", 165);

            migrationBuilder.AddColumn<DateOnly>(
                name: "RegistrationCloseDate",
                table: "Regattas",
                type: "TEXT",
                nullable: true)
                .Annotation("Relational:ColumnOrder", 190);

            migrationBuilder.AlterColumn<string>(
                name: "ImageId",
                table: "OrganizingAuthorities",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true)
                .Annotation("Relational:ColumnOrder", 140)
                .OldAnnotation("Relational:ColumnOrder", 130);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "OrganizingAuthorities",
                type: "TEXT",
                maxLength: 1000,
                nullable: true)
                .Annotation("Relational:ColumnOrder", 130);

            migrationBuilder.AddPrimaryKey(
                name: "PK_LinkTypes",
                table: "LinkTypes",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "OverridePermissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    ActionName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    ExpiresAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: true),
                    IsAllowed = table.Column<bool>(type: "INTEGER", nullable: false),
                    Reason = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ScopedEntityType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    TargetId = table.Column<string>(type: "TEXT", nullable: false),
                    UserId = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OverridePermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OverridePermissions_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OverridePermissions_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OverridePermissions_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OverridePermissions_CreatedById",
                table: "OverridePermissions",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_OverridePermissions_UpdatedById",
                table: "OverridePermissions",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_OverridePermissions_UserId",
                table: "OverridePermissions",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_RegattaExternalLinks_LinkTypes_LinkTypeId",
                table: "RegattaExternalLinks",
                column: "LinkTypeId",
                principalTable: "LinkTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RegattaExternalLinks_LinkTypes_LinkTypeId",
                table: "RegattaExternalLinks");

            migrationBuilder.DropTable(
                name: "OverridePermissions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LinkTypes",
                table: "LinkTypes");

            migrationBuilder.DropColumn(
                name: "ScopedEntityType",
                table: "UserAuthActions");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "EntryFee",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "LateFee",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "LateFeeDate",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "PostalCode",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "RegistrationCloseDate",
                table: "Regattas");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "OrganizingAuthorities");

            migrationBuilder.RenameTable(
                name: "LinkTypes",
                newName: "LinkType");

            migrationBuilder.AlterColumn<string>(
                name: "ImageId",
                table: "OrganizingAuthorities",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true)
                .Annotation("Relational:ColumnOrder", 130)
                .OldAnnotation("Relational:ColumnOrder", 140);

            migrationBuilder.AddPrimaryKey(
                name: "PK_LinkType",
                table: "LinkType",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_RegattaExternalLinks_LinkType_LinkTypeId",
                table: "RegattaExternalLinks",
                column: "LinkTypeId",
                principalTable: "LinkType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
