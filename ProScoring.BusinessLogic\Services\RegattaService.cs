using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities.RegattaEntities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.BusinessLogic.Services;

/// <summary>
/// Service for managing regattas.
/// </summary>
public class RegattaService : IRegattaService
{
    #region Fields

    private readonly ApplicationDbContext _context;
    private readonly IFileService _fileService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<RegattaService> _logger;

    #endregion Fields

    #region Constructor

    /// <summary>
    /// Initializes a new instance of the <see cref="RegattaService"/> class.
    /// </summary>
    /// <param name="context">The database context.</param>
    /// <param name="fileService">The file service.</param>
    /// <param name="httpContextAccessor">The HTTP context accessor.</param>
    /// <param name="logger">The logger.</param>
    public RegattaService(
        ApplicationDbContext context,
        IFileService fileService,
        IHttpContextAccessor httpContextAccessor,
        ILogger<RegattaService> logger
    )
    {
        _context = context;
        _fileService = fileService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    #endregion Constructor

    #region Methods

    /// <summary>
    /// Creates a new regatta for the current user's organizing authority.
    /// </summary>
    /// <param name="dto">The regatta creation data.</param>
    /// <returns>The created regatta.</returns>
    /// <exception cref="UnauthorizedAccessException">Thrown when the user doesn't have permission to create regattas.</exception>
    /// <exception cref="ArgumentException">Thrown when the DTO is invalid.</exception>
    public async Task<Regatta> CreateAsync(RegattaCreateDto dto)
    {
        // Validate the DTO
        var validationErrors = await ValidateAsync(dto);
        if (validationErrors.Any())
        {
            throw new ArgumentException($"Validation failed: {string.Join(", ", validationErrors)}");
        }

        // Get the current user's organizing authority
        var organizingAuthorityId = await GetUserOrganizingAuthorityAsync();
        if (string.IsNullOrEmpty(organizingAuthorityId))
        {
            throw new UnauthorizedAccessException(
                "User does not have access to any organizing authority or is not authenticated."
            );
        }

        // Create the regatta entity
        var regatta = dto.ToEntity(organizingAuthorityId);

        // Handle logo upload if provided
        if (!string.IsNullOrWhiteSpace(dto.LogoDataUri) && !string.IsNullOrWhiteSpace(dto.LogoFileName))
        {
            try
            {
                var result = await _fileService.UploadFromDataUriAsync(
                    dto.LogoFileName,
                    "Logo for " + dto.Name,
                    dto.LogoDataUri
                );
                if (result != null)
                {
                    regatta.EventLogoId = result.Id;
                }
                else
                {
                    _logger.LogWarning("Failed to upload logo for Regatta {RegattaName}", dto.Name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Failed to upload logo for Regatta {RegattaName}. Continuing to create regatta\r\n\tErrorMessage: ({ErrorMessage})",
                    dto.Name,
                    ex.Message
                );
            }
        }

        try
        {
            // Add the regatta to the context
            _context.Regattas.Add(regatta);
            await _context.SaveChangesAsync();

            // Create external links if provided
            if (dto.ExternalLinks.Any())
            {
                await CreateExternalLinksAsync(regatta.Id!, dto.ExternalLinks);
            }

            _logger.LogInformation(
                "Successfully created regatta {RegattaName} with ID {RegattaId}",
                regatta.Name,
                regatta.Id
            );
            return regatta;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create regatta {RegattaName}", dto.Name);
            throw;
        }
    }

    /// <summary>
    /// Gets a regatta by its ID.
    /// </summary>
    /// <param name="id">The regatta ID.</param>
    /// <returns>The regatta if found and accessible, otherwise null.</returns>
    public async Task<Regatta?> GetByIdAsync(string id)
    {
        return await _context
            .Regattas.Include(r => r.OrganizingAuthority)
            .Include(r => r.EventLogo)
            .Include(r => r.ExternalLinks)
            .ThenInclude(el => el.Type)
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    /// <summary>
    /// Validates a regatta creation DTO.
    /// </summary>
    /// <param name="dto">The DTO to validate.</param>
    /// <returns>A list of validation errors, empty if valid.</returns>
    public async Task<List<string>> ValidateAsync(RegattaCreateDto dto)
    {
        var errors = new List<string>();

        // Basic validation
        if (string.IsNullOrWhiteSpace(dto.Name))
        {
            errors.Add("Name is required");
        }

        if (dto.StartDate > dto.EndDate)
        {
            errors.Add("Start date must be on or before end date");
        }

        if (dto.LateFeeDate < dto.StartDate)
        {
            errors.Add("Late fee date must be on or after start date");
        }

        if (dto.RegistrationCloseDate < dto.StartDate)
        {
            errors.Add("Registration close date must be on or after start date");
        }

        // Validate competition days if provided
        if (dto.CompetitionDays != null && dto.CompetitionDays.Any())
        {
            var invalidDays = dto.CompetitionDays.Where(d => d < dto.StartDate || d > dto.EndDate);
            if (invalidDays.Any())
            {
                errors.Add("All competition days must fall between start and end dates");
            }
        }

        // Validate external links count (max 10 unless privileged user)
        if (dto.ExternalLinks.Count > 10)
        {
            var isHmfic =
                _httpContextAccessor.HttpContext?.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true")
                ?? false;
            if (!isHmfic)
            {
                errors.Add("Maximum of 10 external links allowed");
            }
        }

        // Validate external link types exist
        if (dto.ExternalLinks.Any())
        {
            var linkTypeIds = dto.ExternalLinks.Select(el => el.LinkTypeId).Distinct().ToList();
            var existingLinkTypes = await _context
                .LinkTypes.Where(lt => linkTypeIds.Contains(lt.Id))
                .Select(lt => lt.Id)
                .ToListAsync();

            var invalidLinkTypes = linkTypeIds.Except(existingLinkTypes);
            if (invalidLinkTypes.Any())
            {
                errors.Add($"Invalid link types: {string.Join(", ", invalidLinkTypes)}");
            }
        }

        return errors;
    }

    #endregion Methods

    #region Private Methods

    /// <summary>
    /// Gets the organizing authority ID that the current user has manage permissions for.
    /// </summary>
    /// <returns>The organizing authority ID, or null if not found.</returns>
    private async Task<string?> GetUserOrganizingAuthorityAsync()
    {
        var currentUserId = _httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (string.IsNullOrEmpty(currentUserId))
        {
            return null;
        }

        // Check if user has HMFIC claim which grants all permissions
        var isHmfic =
            _httpContextAccessor.HttpContext?.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true")
            ?? false;
        if (isHmfic)
        {
            // For HMFIC users, we need to determine which OA they want to create the regatta for
            // For now, we'll throw an exception requiring explicit OA selection
            throw new InvalidOperationException(
                "HMFIC users must specify which organizing authority to create the regatta for"
            );
        }

        // Get the first organizing authority the user has ADMIN or EDIT permissions for
        var authorizedOaId = await _context
            .UserAuthActions.Where(uaa =>
                uaa.UserId == currentUserId
                && (uaa.AuthActionName == AuthTypes.Actions.ADMIN || uaa.AuthActionName == AuthTypes.Actions.EDIT)
                && uaa.TargetId.StartsWith("O")
            ) // Organizing Authority IDs start with "O"
            .Select(uaa => uaa.TargetId)
            .FirstOrDefaultAsync();

        return authorizedOaId;
    }

    /// <summary>
    /// Creates external links for a regatta.
    /// </summary>
    /// <param name="regattaId">The regatta ID.</param>
    /// <param name="externalLinks">The external links to create.</param>
    private async Task CreateExternalLinksAsync(string regattaId, List<ExternalLinkDto> externalLinks)
    {
        var links = externalLinks
            .Select(
                (link, index) =>
                    new RegattaExternalLink
                    {
                        RegattaId = regattaId,
                        LinkTypeId = link.LinkTypeId,
                        Url = link.Url,
                    }
            )
            .ToList();

        _context.RegattaExternalLinks.AddRange(links);
        await _context.SaveChangesAsync();
    }

    #endregion Private Methods
}
