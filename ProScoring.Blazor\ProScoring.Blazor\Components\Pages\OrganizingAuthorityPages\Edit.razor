@page "/organizingauthorities/edit"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using ProScoring.Blazor.Extensions
@using ProScoring.BusinessLogic.ServiceInterfaces
@using ProScoring.Domain.Dtos
@using ProScoring.Domain.Entities
@using ProScoring.Infrastructure.Authorization
@using Ra<PERSON>zen
@using Radzen.Blazor
@using Radzen.Blazor.Rendering
@using System.Security.Claims
@attribute [Authorize(Policy = EditAuthorizationForPageWithIdHandler.PolicyName)]
@inject IOrganizingAuthorityService OrganizingAuthorityService
@inject NavigationManager navigationManager
@inject TooltipService TooltipService
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Edit: @(dto?.Name ?? "Loading...")</PageTitle>

<RadzenRow JustifyContent="JustifyContent.Center" Style="min-height: 100vh; padding: 2rem;">
    <RadzenColumn Size="12" SizeMD="10" SizeLG="8">
        <RadzenCard Style="box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 8px;">
            @if (dto != null)
            {
                <RadzenStack Gap="1rem" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Start">
                    <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Center">
                        @if (!string.IsNullOrEmpty(dto?.ImageId))
                        {
                            <RadzenImage Path="@($"/api/file/download/{dto.ImageId}")" AlternateText="@($"{dto.Name} Burgee")"
                                Style="width: 100px; height: 100px; object-fit: contain;" />
                        }
                        else
                        {
                            <RadzenIcon Icon="hide_image" />
                        }
                        <RadzenButton Text="Change Burgee" ButtonStyle="ButtonStyle.Secondary"
                            Click="@(() => showImageUpload = true)" Style="margin-bottom: 1rem;" />
                    </RadzenStack>
                    <RadzenText TextStyle="TextStyle.H3">
                        Edit: @(dto?.Name ?? "Loading...")
                    </RadzenText>
                </RadzenStack>

                <RadzenTemplateForm TItem="OrganizingAuthorityUploadDto" Data="@dto" Submit="@HandleValidSubmit">
                    <RadzenFieldset>
                        <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" JustifyContent="JustifyContent.Right"
                            AlignItems="AlignItems.Center" Style="margin-top: 0.5rem;">
                            @if (IsHmfic)
                            {
                                <RadzenLabel Text="Approved" Component="Approved" />
                                <RadzenSwitch @bind-Value=@dto.Approved Name="Approved"
                                    @attributes="@("oa-approved-checkbox".AsTestId())" />
                                <RadzenIcon Icon="info" Style="cursor: pointer;"
                                    MouseEnter="@(args => ShowTooltip(args,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     "Approved OAs are visible to all users.",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     new TooltipOptions
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Delay = 500,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Position = TooltipPosition.Left
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     }))" />
                                                }
                            <RadzenLabel Text="Private" Component="Private" />
                            <RadzenSwitch @bind-Value=@dto.Private Name="Private"
                                @attributes="@("oa-private-checkbox".AsTestId())" />
                            <RadzenIcon Icon="info" Style="cursor: pointer;"
                                MouseEnter="@(args => ShowTooltip(args,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     "Private OAs are not discoverable by search.",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     new TooltipOptions
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Delay = 500,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Position = TooltipPosition.Left
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     }))" />
                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem">
                        <RadzenFormField Text="Club (or OA) Name">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.Name" Name="Name" Placeholder="Name"
                                    Style="width: 100%;" @onblur="StateHasChanged"
                                    @attributes="@("oa-name-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="Name" Text="Name is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="Website">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.Website" Name="Website" Placeholder="Website"
                                    Style="width: 100%" @attributes="@("oa-website-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="Website" Text="Website is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="Email">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.Email" Name="Email" Placeholder="Email"
                                    Style="width: 100%;" @attributes="@("oa-email-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="Email" Text="Email is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="Phone">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.Phone" Name="Phone" Placeholder="Phone"
                                    Style="width: 100%;" @attributes="@("oa-phone-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="Phone" Text="Phone is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="Address Line 1">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.AddressLine1" Name="AddressLine1"
                                    Placeholder="Address Line 1" Style="width: 100%;"
                                    @attributes="@("oa-address1-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="AddressLine1" Text="Address Line 1 is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="Address Line 2">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.AddressLine2" Name="AddressLine2"
                                    Placeholder="Address Line 2" Style="width: 100%;"
                                    @attributes="@("oa-address2-input".AsTestId())" />
                            </ChildContent>
                        </RadzenFormField>
                        <RadzenFormField Text="City">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.City" Name="City" Placeholder="City"
                                    Style="width: 100%;" @attributes="@("oa-city-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="City" Text="City is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="State">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.State" Name="State" Placeholder="State"
                                    Style="width: 100%;" @attributes="@("oa-state-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="State" Text="State is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="Postal Code">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.PostalCode" Name="PostalCode" Placeholder="Postal Code"
                                    Style="width: 100%;" @attributes="@("oa-postal-code-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="PostalCode" Text="Postal Code is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="Country">
                            <ChildContent>
                                <RadzenTextBox @bind-Value="dto.Country" Name="Country" Placeholder="Country"
                                    Style="width: 100%;" @attributes="@("oa-country-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenRequiredValidator Component="Country" Text="Country is required"
                                    Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        <RadzenFormField Text="Description">
                            <ChildContent>
                                <RadzenTextArea @bind-Value="dto.Description" Name="Description" Placeholder="Description"
                                    Style="width: 100%; min-height: 100px;" MaxLength="1000"
                                    @attributes="@("oa-description-input".AsTestId())" />
                            </ChildContent>
                            <Helper>
                                <RadzenLengthValidator Component="Description" Max="1000"
                                    Text="Description cannot exceed 1000 characters" Style="position: absolute;" />
                            </Helper>
                        </RadzenFormField>
                        @if (showImageUpload)
                            {
                                <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Center">
                                    <RadzenCard Style="width: 100%; max-width: 400px;" Class="mb-4">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                            <RadzenText TextStyle="TextStyle.H6">Select New Burgee File</RadzenText>
                                            <RadzenFileInput @bind-Value=@dto.BurgeeDataUri @bind-FileSize=@fileSize
                                                @bind-FileName=@dto.BurgeeFileName MaxFileSize="1048576" TValue="string"
                                                Style="width: 100%" Error=@(args => OnError(args, "FileInput"))
                                                InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "select burgee file" } }.Union("oa-burgee-input".AsTestId()).ToDictionary(k => k.Key, v => v.Value))" />
                                            <RadzenButton Text="Cancel" ButtonStyle="ButtonStyle.Light"
                                                Click="@(() => CancelImageUpload())"
                                                @attributes="@("cancel-image-upload-button".AsTestId())" />
                                        </RadzenStack>
                                    </RadzenCard>
                                </RadzenStack>
                            }
                        </RadzenStack>
                    </RadzenFieldset>

                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween"
                        Gap="1rem" Class="mt-4">
                        <div>
                            @if (IsHmfic)
                            {
                                <RadzenButton Text="List" ButtonStyle="ButtonStyle.Light" Click="@GoToList" Class="ml-2"
                                    @attributes="@("oa-list-button".AsTestId())" />
                            }
                        </div>
                        <div class="gap-3">
                            <RadzenButton Text="Back" ButtonStyle="ButtonStyle.Light" Click="@GoBack"
                                @attributes="@("oa-back-button".AsTestId())" />
                            <RadzenButton Text="Save" ButtonType="ButtonType.Submit" ButtonStyle="ButtonStyle.Primary"
                                @attributes="@("save-oa-button".AsTestId())" />
                        </div>
                    </RadzenStack>
                </RadzenTemplateForm>
            }
            else
            {
                <RadzenProgressBar Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" />
            }
        </RadzenCard>
    </RadzenColumn>
</RadzenRow>

@code {
    [SupplyParameterFromQuery(Name = "id")]
    public string? Id { get; set; }
    private OrganizingAuthorityUploadDto? dto;
    private string? imageId;
    private bool showImageUpload;
    private long? fileSize;
    private bool IsHmfic { get; set; }

    /// <summary>
    /// Initializes the component and loads organizing authority data.
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        // Check if the user has HMFIC rights
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        IsHmfic = authState.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true");

        if (!string.IsNullOrEmpty(Id))
        {
            var entity = await OrganizingAuthorityService.GetByIdAsync(Id);
            if (entity != null)
            {
                dto = OrganizingAuthorityUploadDto.FromEntity(entity);
                imageId = entity.ImageId;
            }
        }
    }

    /// <summary>
    /// Handles the form submission when valid.
    /// </summary>
    /// <param name="updatedDto">The updated organizing authority data.</param>
    private async Task HandleValidSubmit(OrganizingAuthorityUploadDto updatedDto)
    {
        // deal with image change
        if (showImageUpload && !string.IsNullOrEmpty(updatedDto.BurgeeDataUri))
        {
            if (!string.IsNullOrEmpty(updatedDto.ImageId))
            {
                updatedDto.ImageId = null; // clear it so the new one will get written.
            }
        }
        await OrganizingAuthorityService.UpdateAsync(updatedDto);
        navigationManager.NavigateTo("/organizingauthorities");
    }

    /// <summary>
    /// Shows a tooltip at the specified element.
    /// </summary>
    /// <param name="elementReference">The element to attach the tooltip to.</param>
    /// <param="text">The tooltip text.</param>
    /// <param="options">Optional tooltip configuration options.</param>
    private void ShowTooltip(ElementReference elementReference, string text, TooltipOptions? options = null)
    {
        TooltipService.Open(elementReference, text, options);
    }

    /// <summary>
    /// Cancels the image upload and clears related fields.
    /// </summary>
    private void CancelImageUpload()
    {
        showImageUpload = false;
        dto!.BurgeeDataUri = string.Empty;
        dto!.BurgeeFileName = string.Empty;
    }

    /// <summary>
    /// Handles file upload errors.
    /// </summary>
    /// <param name="args">The error event arguments.</param>
    /// <param name="name">The name of the component that raised the error.</param>
    private void OnError(UploadErrorEventArgs args, string name)
    {
        Console.WriteLine($"{args.Message}");
    }

    /// <summary>
    /// Navigates back to the previous page.
    /// </summary>
    private void GoBack()
    {
        navigationManager.NavigateTo("javascript:window.history.back()");
    }

    /// <summary>
    /// Navigates to the organizing authorities list page.
    /// </summary>
    private void GoToList()
    {
        navigationManager.NavigateTo("/organizingauthorities");
    }
}
