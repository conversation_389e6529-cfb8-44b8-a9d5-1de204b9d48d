using System;
using FluentAssertions;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit;
using Xunit.Abstractions;

namespace ProScoring.Tests.Infrastructure;

/// <summary>
/// Tests for the ApplicationDbContext class.
/// These tests run in serial to ensure consistent ID generation behavior.
/// </summary>
[Collection(nameof(StaticIdGenerationUtilServiceForTesting))]
public sealed class ApplicationDbContextTests
{
    #region Test Data
    /// <summary>
    /// Gets a simple file record for testing purposes.
    /// </summary>
    private static FileRecord SimpleFileRecord
    {
        get
        {
            return new FileRecord
            {
                Id = Guid.NewGuid().ToString()[..10],
                TrustedFileNameForDisplay = "test.txt",
                Path = "test/random_path.xyz",
                ContentType = "text/plain",
                Size = 12345,
                UntrustedName = "test.txt",
                UploadDate = new DateTimeOffset(1776, 7, 4, 12, 0, 0, TimeSpan.Zero),
            };
        }
    }
    #endregion

    #region Fields
    private readonly IIdGenerationUtilService _mockIdService;
    private readonly ITestOutputHelper _output;
    private readonly ServiceCollection _services;
    #endregion

    #region Constructor
    /// <summary>
    /// Initializes a new instance of the <see cref="ApplicationDbContextTests"/> class.
    /// Sets up test dependencies using SQLite in-memory database.
    /// </summary>
    /// <param name="output">The test output helper.</param>
    public ApplicationDbContextTests(ITestOutputHelper output)
    {
        _output = output;

        _services = new ServiceCollection();
        _services.AddTransient(_ => Substitute.For<AuthenticationStateProvider>());

        _mockIdService = Substitute.For<IIdGenerationUtilService>();
        _mockIdService.GenerateId(Arg.Any<IHasAutoInsertedId>()).Returns("123");

        _services.AddSingleton<IIdGenerationUtilService>(
            StaticIdGenerationUtilServiceForTesting.GetInstanceAndConfigure(_mockIdService)
        );
        _services.AddTransient<IValueGenerator, CustomIdValueGenerator>();
        _services.AddTransientSafeLogger<ApplicationDbContext>(_output);
        _services.AddTransientSafeLogger<CustomIdValueGenerator>(_output);
        _services.AddTransient<IDateTimeOffsetProvider>(_ => new FixedDateTimeOffsetProvider(1776, 7, 4));
        _services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseSqlite("DataSource=:memory:;Cache=Shared");
        });
    }
    #endregion

    #region Test Methods
    /// <summary>
    /// Tests that the ApplicationDbContext can be instantiated successfully.
    /// </summary>
    [Fact]
    public void ApplicationDbContext_CanCreateInstance()
    {
        // Arrange
        var serviceProvider = _services.BuildServiceProvider();

        // Act
        using var dbContext = serviceProvider.GetRequiredService<ApplicationDbContext>();

        // Assert
        dbContext.Should().NotBeNull();
    }

    /// <summary>
    /// Tests that the Files DbSet is properly configured.
    /// </summary>
    [Fact]
    public void ApplicationDbContext_HasFilesDbSet()
    {
        // Arrange
        using var dbContext = GetDbContext();

        // Act
        var files = dbContext.Files;

        // Assert
        files.Should().NotBeNull();
    }

    /// <summary>
    /// Tests that a FileRecord can be added, updated, and retrieved correctly.
    /// </summary>
    [Fact]
    public void FileRecord_AddUpdateAndRetrieveSuccessfully()
    {
        // Arrange
        using var dbContext = GetDbContext();
        var fileRecord = SimpleFileRecord;

        // Act
        dbContext.Files.Add(fileRecord);
        dbContext.SaveChanges();
        var retrievedFile = dbContext.Files.Find(fileRecord.Id);

        // Assert
        retrievedFile.Should().NotBeNull();
        retrievedFile
            .Should()
            .BeEquivalentTo(
                new
                {
                    fileRecord.ContentType,
                    fileRecord.Id,
                    fileRecord.Note,
                    fileRecord.Path,
                    fileRecord.Size,
                    fileRecord.UntrustedName,
                    fileRecord.UploadDate,
                }
            );
    }

    /// <summary>
    /// Tests that the IdUtilsService is called exactly once when adding a file.
    /// </summary>
    [Fact]
    public void FileAdd_CallsIdUtilsServiceOnce()
    {
        // Arrange
        using var dbContext = GetDbContext();
        var fileRecord = new FileRecord
        {
            // Don't set Id - let Entity Framework generate it
            TrustedFileNameForDisplay = "test.txt",
            Path = "test/random_path.xyz",
            ContentType = "text/plain",
            Size = 12345,
            UntrustedName = "test.txt",
            UploadDate = new DateTimeOffset(1776, 7, 4, 12, 0, 0, TimeSpan.Zero),
        };

        // Act
        dbContext.Files.Add(fileRecord);
        dbContext.SaveChanges();

        // Assert
        _mockIdService.Received(1).GenerateId(Arg.Any<IHasAutoInsertedId>());
    }

    /// <summary>
    /// Tests that the IdUtilsService is called exactly once when adding an organizing authority.
    /// </summary>
    [Fact]
    public void OrganizingAuthorityAdd_CallsIdUtilsServiceOnce()
    {
        // Arrange
        using var dbContext = GetDbContext();
        var authority = new OrganizingAuthority
        {
            // Don't set Id - let Entity Framework generate it
            Name = "Test Authority",
        };

        // Act
        dbContext.OrganizingAuthorities.Add(authority);
        dbContext.SaveChanges();

        // Assert
        _mockIdService.Received(1).GenerateId(Arg.Any<IHasAutoInsertedId>());
    }
    #endregion

    #region Helper Methods
    /// <summary>
    /// Gets a configured instance of ApplicationDbContext for testing.
    /// </summary>
    /// <returns>A new instance of ApplicationDbContext.</returns>
    private ApplicationDbContext GetDbContext()
    {
        var serviceProvider = _services.BuildServiceProvider();
        var context = serviceProvider.GetRequiredService<ApplicationDbContext>();
        context.Database.OpenConnection();
        context.Database.EnsureCreated();
        return context;
    }
    #endregion
}
