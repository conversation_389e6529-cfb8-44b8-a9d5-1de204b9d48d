using System.Security.Claims;
using Bunit;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using NSubstitute;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Tests.Blazor.Components;
using ProScoring.Tests.Helpers;
using Radzen;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the navigation functionality in Organizing Authority pages.
/// Verifies that navigation buttons are displayed correctly based on user permissions.
/// </summary>
public class OrganizingAuthorityNavigationTests : TestContext
{
    #region Fields

    private readonly IAuthorizationService _mockAuthorizationService;
    private readonly IOrganizingAuthorityService _mockOrganizingAuthorityService;
    private readonly TestAuthenticationStateProvider _authStateProvider;

    #endregion Fields

    #region Constructor

    public OrganizingAuthorityNavigationTests()
    {
        _mockOrganizingAuthorityService = Substitute.For<IOrganizingAuthorityService>();
        _mockAuthorizationService = Substitute.For<IAuthorizationService>();
        _authStateProvider = new TestAuthenticationStateProvider();

        ConfigureServices();
    }

    #endregion Constructor

    #region Test Methods

    /// <summary>
    /// Verifies that regular users can see the back button but not the list button when creating an authority.
    /// </summary>
    [Fact]
    public void Create_NavigationLinks_ForRegularUser()
    {
        // Arrange
        SetupRegularUser();
        SetupOrganizingAuthorityService();

        // Act
        var cut = RenderComponent<OrganizingAuthorityNavigationComponentTests>(parameters =>
            parameters.Add(p => p.IsHmfic, false)
        );

        // Assert
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(0, "List button should not be visible for regular users");
    }

    /// <summary>
    /// Verifies that HMFIC users can see both back and list buttons when creating an authority.
    /// </summary>
    [Fact]
    public void Create_NavigationLinks_ForHmficUser()
    {
        // Arrange
        SetupHmficUser();
        SetupOrganizingAuthorityService();

        // Act
        var cut = RenderComponent<OrganizingAuthorityNavigationComponentTests>(parameters =>
            parameters.Add(p => p.IsHmfic, true)
        );

        // Assert
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(1, "List button should be visible for HMFIC users");
    }

    /// <summary>
    /// Verifies that regular users can see the back button but not the list button when editing an authority.
    /// </summary>
    [Fact]
    public void Edit_NavigationLinks_ForRegularUser()
    {
        // Arrange
        SetupRegularUser();
        SetupOrganizingAuthorityForEdit();

        // Act
        var cut = RenderComponent<OrganizingAuthorityNavigationComponentTests>(parameters =>
            parameters.Add(p => p.IsHmfic, false)
        );

        // Assert
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(0, "List button should not be visible for regular users");
    }

    /// <summary>
    /// Verifies that HMFIC users can see both back and list buttons when editing an authority.
    /// </summary>
    [Fact]
    public void Edit_NavigationLinks_ForHmficUser()
    {
        // Arrange
        SetupHmficUser();
        SetupOrganizingAuthorityForEdit();

        // Act
        var cut = RenderComponent<OrganizingAuthorityNavigationComponentTests>(parameters =>
            parameters.Add(p => p.IsHmfic, true)
        );

        // Assert
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(1, "List button should be visible for HMFIC users");
    }

    #endregion Test Methods

    #region Helper Methods

    private void ConfigureServices()
    {
        // Add authorization services first
        Services.AddAuthorizationCore(options =>
        {
            options.AddPolicy(
                EditAuthorizationForPageWithIdHandler.PolicyName,
                policy => policy.RequireAssertion(_ => true)
            );
            options.AddPolicy(
                EditAuthorizationForResourceHandler.PolicyName,
                policy => policy.RequireAssertion(_ => true)
            );
        });

        // Add core services
        Services.AddSingleton(_mockOrganizingAuthorityService);
        Services.AddSingleton(_mockAuthorizationService);
        Services.AddSingleton<AuthenticationStateProvider>(_authStateProvider);

        // Add Radzen services
        Services.AddScoped<DialogService>();
        Services.AddScoped<NotificationService>();
        Services.AddScoped<TooltipService>();

        // Set up JSInterop for Radzen components
        JSInterop.Mode = JSRuntimeMode.Loose;
        JSInterop.SetupVoid("Radzen.uploads", _ => true);
        JSInterop.SetupVoid("Radzen.toggleClass", _ => true);
        JSInterop.SetupVoid("Radzen.closePopup", _ => true);
        JSInterop.SetupVoid("Radzen.openPopup", _ => true);
        JSInterop.SetupVoid("Radzen.destroyPopup", _ => true);

        // Set timeout
        TestContext.DefaultWaitTimeout = TimeSpan.FromSeconds(5);
    }

    private void SetupRegularUser()
    {
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "user1") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));
    }

    private void SetupHmficUser()
    {
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "admin1"), new Claim(AuthTypes.HMFIC, "true") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));
    }

    private void SetupOrganizingAuthorityService()
    {
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };
        _mockOrganizingAuthorityService
            .CreateAsync(Arg.Any<OrganizingAuthorityUploadDto>())
            .Returns(Task.FromResult(organizingAuthority));
    }

    private void SetupOrganizingAuthorityForEdit()
    {
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };
        _mockOrganizingAuthorityService.GetByIdAsync("O1").Returns(organizingAuthority);
    }

    #endregion Helper Methods
}
