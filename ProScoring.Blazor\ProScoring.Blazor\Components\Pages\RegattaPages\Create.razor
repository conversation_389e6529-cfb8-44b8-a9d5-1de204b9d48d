@page "/regattas/create"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using ProScoring.Blazor.Extensions
@using ProScoring.BusinessLogic.ServiceInterfaces
@using ProScoring.Domain.Dtos
@using ProScoring.Domain.Entities
@using ProScoring.Infrastructure.Authorization
@using Radzen
@using Radzen.Blazor
@using System.Security.Claims
@attribute [Authorize]
@inject IRegattaService RegattaService
@inject NavigationManager NavigationManager
@inject TooltipService TooltipService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NotificationService NotificationService

<PageTitle>Create Regatta</PageTitle>

<RadzenStack Gap="1rem">
    <RadzenBreadCrumb>
        <RadzenBreadCrumbItem Path="/" Text="Home" />
        <RadzenBreadCrumbItem Text="Create Regatta" />
    </RadzenBreadCrumb>

    <RadzenText TextStyle="TextStyle.H3" TagName="TagName.H1">Create New Regatta</RadzenText>

    <RadzenTemplateForm TItem="RegattaCreateDto" Data="@dto" Submit="@OnSubmit">
        <RadzenStack Gap="1rem">
            <!-- Basic Information -->
            <RadzenFieldset Text="Basic Information">
                <RadzenStack Gap="1rem">
                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Name" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.Name" Style="width: 100%;"
                                        @attributes="@("regatta-name-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="Name" Text="Name is required" />
                                    <RadzenLengthValidator Component="Name" Min="3" Max="255"
                                        Text="Name must be between 3 and 255 characters" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Venue Name" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.VenueName" Style="width: 100%;"
                                        @attributes="@("regatta-venue-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="VenueName" Text="Venue name is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>

                    <RadzenRow>
                        <RadzenColumn Size="12">
                            <RadzenFormField Text="Description" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextArea @bind-Value="@dto.Description" Style="width: 100%; height: 100px;"
                                        @attributes="@("regatta-description-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenLengthValidator Component="Description" Max="1000"
                                        Text="Description cannot exceed 1000 characters" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>

                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Start Date" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenDatePicker @bind-Value="@startDate" Style="width: 100%;"
                                        @attributes="@("regatta-start-date-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="StartDate" Text="Start date is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="End Date" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenDatePicker @bind-Value="@endDate" Style="width: 100%;"
                                        @attributes="@("regatta-end-date-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="EndDate" Text="End date is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>

                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Website" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.Website" Style="width: 100%;"
                                        @attributes="@("regatta-website-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenEmailValidator Component="Website" Text="Please enter a valid URL" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenStack>
            </RadzenFieldset>

            <!-- Fees and Dates -->
            <RadzenFieldset Text="Registration & Fees">
                <RadzenStack Gap="1rem">
                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Entry Fee" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenNumeric @bind-Value="@dto.EntryFee" Style="width: 100%;" Format="C"
                                        @attributes="@("regatta-entry-fee-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="EntryFee" Text="Entry fee is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Late Fee" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenNumeric @bind-Value="@dto.LateFee" Style="width: 100%;" Format="C"
                                        @attributes="@("regatta-late-fee-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="LateFee" Text="Late fee is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>

                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Late Fee Date" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenDatePicker @bind-Value="@lateFeeDate" Style="width: 100%;"
                                        @attributes="@("regatta-late-fee-date-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="LateFeeDate" Text="Late fee date is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Registration Close Date" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenDatePicker @bind-Value="@registrationCloseDate" Style="width: 100%;"
                                        @attributes="@("regatta-registration-close-date-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="RegistrationCloseDate"
                                        Text="Registration close date is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenStack>
            </RadzenFieldset>

            <!-- Address Information -->
            <RadzenFieldset Text="Location">
                <RadzenStack Gap="1rem">
                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Address Line 1" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.AddressLine1" Style="width: 100%;"
                                        @attributes="@("regatta-address1-input".AsTestId())" />
                                </ChildContent>
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Address Line 2" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.AddressLine2" Style="width: 100%;"
                                        @attributes="@("regatta-address2-input".AsTestId())" />
                                </ChildContent>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>

                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="4">
                            <RadzenFormField Text="City" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.City" Style="width: 100%;"
                                        @attributes="@("regatta-city-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="City" Text="City is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="4">
                            <RadzenFormField Text="State/Province" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.State" Style="width: 100%;"
                                        @attributes="@("regatta-state-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="State" Text="State/Province is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="4">
                            <RadzenFormField Text="Postal Code" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.PostalCode" Style="width: 100%;"
                                        @attributes="@("regatta-postal-code-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="PostalCode" Text="Postal code is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>

                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenFormField Text="Country" Variant="Variant.Outlined">
                                <ChildContent>
                                    <RadzenTextBox @bind-Value="@dto.Country" Style="width: 100%;"
                                        @attributes="@("regatta-country-input".AsTestId())" />
                                </ChildContent>
                                <Helper>
                                    <RadzenRequiredValidator Component="Country" Text="Country is required" />
                                </Helper>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenStack>
            </RadzenFieldset>

            <!-- Logo Upload -->
            <RadzenFieldset Text="Regatta Logo">
                <RadzenStack Gap="1rem">
                    <RadzenText TextStyle="TextStyle.Body1">
                        Upload a logo for your regatta (optional)
                    </RadzenText>
                    <RadzenFileInput @bind-Value=@dto.LogoDataUri @bind-FileSize=@fileSize
                        @bind-FileName=@dto.LogoFileName MaxFileSize="1048576" TValue="string" Style="width: 100%"
                        Error=@(args => OnError(args, "FileInput"))
                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "select regatta logo file" }, { "data-testid", "regatta-logo-input" } })" />
                    <RadzenText class="text-center" style="color: var(--rz-text-tertiary-color);">
                        (optional, max 1MB)
                    </RadzenText>
                </RadzenStack>
            </RadzenFieldset>

            <!-- Submit Buttons -->
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" Gap="1rem">
                <RadzenButton ButtonType="ButtonType.Button" Variant="Variant.Text"
                    Click="@(() => NavigationManager.NavigateTo("/"))"
                    @attributes="@("regatta-cancel-button".AsTestId())">
                    Cancel
                </RadzenButton>
                <RadzenButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" IsBusy="@isSubmitting"
                    BusyText="Creating..." @attributes="@("regatta-create-button".AsTestId())">
                    Create Regatta
                </RadzenButton>
            </RadzenStack>
        </RadzenStack>
    </RadzenTemplateForm>
</RadzenStack>

@code {
    private RegattaCreateDto dto = new();
    private bool isSubmitting = false;
    private long? fileSize;

    // Date properties for binding to RadzenDatePicker
    private DateTime? startDate
    {
        get => dto.StartDate == default ? null : dto.StartDate.ToDateTime(TimeOnly.MinValue);
        set => dto.StartDate = value.HasValue ? DateOnly.FromDateTime(value.Value) : default;
    }

    private DateTime? endDate
    {
        get => dto.EndDate == default ? null : dto.EndDate.ToDateTime(TimeOnly.MinValue);
        set => dto.EndDate = value.HasValue ? DateOnly.FromDateTime(value.Value) : default;
    }

    private DateTime? lateFeeDate
    {
        get => dto.LateFeeDate == default ? null : dto.LateFeeDate.ToDateTime(TimeOnly.MinValue);
        set => dto.LateFeeDate = value.HasValue ? DateOnly.FromDateTime(value.Value) : default;
    }

    private DateTime? registrationCloseDate
    {
        get => dto.RegistrationCloseDate == default ? null : dto.RegistrationCloseDate.ToDateTime(TimeOnly.MinValue);
        set => dto.RegistrationCloseDate = value.HasValue ? DateOnly.FromDateTime(value.Value) : default;
    }

    private async Task OnSubmit()
    {
        if (isSubmitting) return;

        isSubmitting = true;
        try
        {
            var regatta = await RegattaService.CreateAsync(dto);

            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Success,
                Summary = "Success",
                Detail = "Regatta created successfully!",
                Duration = 4000
            });

            // Redirect to regatta admin page
            NavigationManager.NavigateTo($"/regattas/{regatta.Id}/admin");
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "Error",
                Detail = $"Failed to create regatta: {ex.Message}",
                Duration = 8000
            });
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void OnError(UploadErrorEventArgs args, string name)
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Error,
            Summary = "File Upload Error",
            Detail = $"Error uploading file: {args.Message}",
            Duration = 8000
        });
    }
}
