using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.BusinessLogic.Services;
using ProScoring.Domain.Entities;

namespace ProScoring.BusinessLogic.Extensions;

public static class ServiceConfigurationExtensions
{
    public static IHostApplicationBuilder AddBusinessLogicServices(this IHostApplicationBuilder builder)
    {
        builder.Services.AddScoped<IOrganizingAuthorityService, OrganizingAuthorityService>();
        builder.Services.AddScoped<IRegattaService, RegattaService>();
        return builder;
    }

    public static IHostApplicationBuilder SeedBusinessLogicData(this IHostApplicationBuilder builder)
    {
        // <NAME_EMAIL> to AspNetUsers table if it doesn't exist
        try
        {
            // TODO: This needs to be refactored. It isn't really AddingSerives,
            // but rather doing initial seeding.
            if (!builder.Configuration.GetValue<bool>("CREATING_MIGRATION"))
            {
                builder.SeedDefaultUser();
            }
        }
        catch (Exception ex)
        {
            // Handle exception (e.g., log it)
            Console.WriteLine($"An error occurred while seeding the default user: {ex.Message}");
        }
        return builder;
    }

    // TODO: This needs to go away. It is a hack to get the default user seeded.
    private static IHostApplicationBuilder SeedDefaultUser(this IHostApplicationBuilder builder)
    {
        // add user
        // <NAME_EMAIL> to AspNetUsers table if it doesn't exist
        var serviceProvider = builder.Services.BuildServiceProvider();
        var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();

        Task.Run(async () =>
            {
                var user = await userManager.FindByEmailAsync("<EMAIL>");
                if (user == null)
                {
                    user = new ApplicationUser
                    {
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailConfirmed = true,
                    };
                    await userManager.CreateAsync(user, "YourSecurePassword123!");
                    user.EmailConfirmed = true;
                    await userManager.UpdateAsync(user);
                }
                // add claim HMFIC = true to admin user
                var claim = new Claim("HMFIC", "true");
                var claims = await userManager.GetClaimsAsync(user);
                if (!claims.Any(c => c.Type == claim.Type && c.Value == claim.Value))
                {
                    await userManager.AddClaimAsync(user, claim);
                }
            })
            .GetAwaiter()
            .GetResult();

        return builder;
    }
}
