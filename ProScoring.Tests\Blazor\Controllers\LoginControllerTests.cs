using FluentAssertions;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using ProScoring.Blazor.Controllers;
using ProScoring.Domain.Entities;
using IdentitySignInResult = Microsoft.AspNetCore.Identity.SignInResult;

namespace ProScoring.Tests.Blazor.Controllers;

/// <summary>
/// Tests for the LoginController class, covering authentication scenarios including
/// successful login, two-factor authentication, account lockout, and failed attempts.
/// </summary>
public class LoginControllerTests
{
    #region Fields
    private readonly SignInManager<ApplicationUser> _substituteSignInManager;
    private readonly ILogger<LoginController> _substituteLogger;
    private readonly IAntiforgery _substituteAntiforgery;
    private readonly LoginController _controller;
    #endregion Fields

    #region Constructor
    /// <summary>
    /// Initializes test dependencies including mocked SignInManager, Logger, and Antiforgery services.
    /// </summary>
    public LoginControllerTests()
    {
        var userStoreSubstitute = Substitute.For<IUserStore<ApplicationUser>>();
        var userManagerSubstitute = Substitute.For<UserManager<ApplicationUser>>(
            userStoreSubstitute,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );

        var testUser = new ApplicationUser { UserName = "user" };
        userManagerSubstitute.FindByNameAsync(Arg.Any<string>()).Returns(Task.FromResult<ApplicationUser?>(testUser));
        userManagerSubstitute
            .CheckPasswordAsync(Arg.Any<ApplicationUser>(), Arg.Any<string>())
            .Returns(Task.FromResult(true));

        _substituteSignInManager = Substitute.For<SignInManager<ApplicationUser>>(
            userManagerSubstitute,
            Substitute.For<IHttpContextAccessor>(),
            Substitute.For<IUserClaimsPrincipalFactory<ApplicationUser>>(),
            Substitute.For<IOptions<IdentityOptions>>(),
            Substitute.For<ILogger<SignInManager<ApplicationUser>>>(),
            Substitute.For<Microsoft.AspNetCore.Authentication.IAuthenticationSchemeProvider>(),
            Substitute.For<IUserConfirmation<ApplicationUser>>()
        );

        _substituteLogger = Substitute.For<ILogger<LoginController>>();
        _substituteAntiforgery = Substitute.For<IAntiforgery>();

        _controller = new LoginController(_substituteSignInManager, _substituteLogger, _substituteAntiforgery);
    }
    #endregion Constructor

    #region Tests
    /// <summary>
    /// Tests that login returns BadRequest when model state is invalid.
    /// </summary>
    [Fact]
    public async Task Login_InvalidModelState_ReturnsBadRequest()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Sample error");
        var loginModel = new LoginModel { Username = "user", Password = "password" };

        // Act
        var result = await _controller.Login(loginModel);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        _substituteLogger
            .Received(1)
            .Log(
                LogLevel.Warning,
                Arg.Any<EventId>(),
                Arg.Is<object>(state =>
                    state != null && state.ToString()!.Contains("Invalid model state in login request")
                ),
                null,
                Arg.Any<Func<object, Exception?, string>>()
            );
    }

    /// <summary>
    /// Tests successful login scenario with correct credentials.
    /// </summary>
    [Fact]
    public async Task Login_Successful_ReturnsOkWithSuccessTrue()
    {
        // Arrange
        var loginModel = new LoginModel
        {
            Username = "user",
            Password = "password",
            RememberMe = false,
        };

        _substituteSignInManager
            .PasswordSignInAsync(
                loginModel.Username,
                loginModel.Password,
                loginModel.RememberMe,
                lockoutOnFailure: false
            )
            .Returns(Task.FromResult(IdentitySignInResult.Success));

        // Act
        var result = await _controller.Login(loginModel);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result.As<OkObjectResult>();
        okResult.Value.Should().NotBeNull();
        okResult.Value.Should().BeEquivalentTo(new { success = true });

        _substituteLogger
            .Received(1)
            .Log(
                LogLevel.Information,
                Arg.Any<EventId>(),
                Arg.Is<object>(state =>
                    state != null && state.ToString()!.Contains("User user logged in successfully")
                ),
                null,
                Arg.Any<Func<object, Exception?, string>>()
            );
    }

    /// <summary>
    /// Tests that two-factor authentication requirement is properly handled.
    /// </summary>
    [Fact]
    public async Task Login_RequiresTwoFactor_ReturnsOkWithRequiresTwoFactorTrue()
    {
        // Arrange
        var loginModel = new LoginModel
        {
            Username = "user",
            Password = "password",
            RememberMe = false,
        };

        _substituteSignInManager
            .PasswordSignInAsync(
                loginModel.Username,
                loginModel.Password,
                loginModel.RememberMe,
                lockoutOnFailure: false
            )
            .Returns(Task.FromResult(IdentitySignInResult.TwoFactorRequired));

        // Act
        var result = await _controller.Login(loginModel);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result.As<OkObjectResult>();
        okResult.Value.Should().NotBeNull();
        okResult.Value.Should().BeEquivalentTo(new { success = false, requiresTwoFactor = true });

        _substituteLogger
            .Received(1)
            .Log(
                LogLevel.Information,
                Arg.Any<EventId>(),
                Arg.Is<object>(state =>
                    state != null && state.ToString()!.Contains("User user requires two-factor authentication")
                ),
                null,
                Arg.Any<Func<object, Exception?, string>>()
            );
    }

    /// <summary>
    /// Tests that locked out accounts are properly handled.
    /// </summary>
    [Fact]
    public async Task Login_IsLockedOut_ReturnsOkWithIsLockedOutTrue()
    {
        // Arrange
        var loginModel = new LoginModel
        {
            Username = "user",
            Password = "password",
            RememberMe = false,
        };

        _substituteSignInManager
            .PasswordSignInAsync(
                loginModel.Username,
                loginModel.Password,
                loginModel.RememberMe,
                lockoutOnFailure: false
            )
            .Returns(Task.FromResult(IdentitySignInResult.LockedOut));

        // Act
        var result = await _controller.Login(loginModel);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result.As<OkObjectResult>();
        okResult.Value.Should().NotBeNull();
        okResult.Value.Should().BeEquivalentTo(new { success = false, isLockedOut = true });

        _substituteLogger
            .Received(1)
            .Log(
                LogLevel.Warning,
                Arg.Any<EventId>(),
                Arg.Is<object>(state => state != null && state.ToString()!.Contains("User account user is locked out")),
                null,
                Arg.Any<Func<object, Exception?, string>>()
            );
    }

    /// <summary>
    /// Tests that failed login attempts are properly handled.
    /// </summary>
    [Fact]
    public async Task Login_Failed_ReturnsOkWithSuccessFalseAndMessage()
    {
        // Arrange
        var loginModel = new LoginModel
        {
            Username = "user",
            Password = "password",
            RememberMe = false,
        };

        _substituteSignInManager
            .PasswordSignInAsync(
                loginModel.Username,
                loginModel.Password,
                loginModel.RememberMe,
                lockoutOnFailure: false
            )
            .Returns(Task.FromResult(IdentitySignInResult.Failed));

        // Act
        var result = await _controller.Login(loginModel);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result.As<OkObjectResult>();
        okResult.Value.Should().NotBeNull();

        var resultValue = okResult.Value;
        resultValue.Should().NotBeNull();
        var resultType = resultValue!.GetType();
        var successProp = resultType.GetProperty("success");
        var messageProp = resultType.GetProperty("message");

        successProp.Should().NotBeNull();
        messageProp.Should().NotBeNull();
        successProp!.GetValue(resultValue).Should().Be(false);
        messageProp!.GetValue(resultValue).Should().NotBeNull();

        _substituteLogger
            .Received(1)
            .Log(
                LogLevel.Warning,
                Arg.Any<EventId>(),
                Arg.Is<object>(state =>
                    state != null && state.ToString()!.Contains("Invalid login attempt for user user")
                ),
                null,
                Arg.Any<Func<object, Exception?, string>>()
            );
    }
    #endregion Tests
}
