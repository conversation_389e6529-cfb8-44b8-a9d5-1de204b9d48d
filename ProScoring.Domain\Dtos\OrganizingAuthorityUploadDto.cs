using Microsoft.AspNetCore.Identity;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Dtos;

public class OrganizingAuthorityUploadDto : IHasId
{
    #region properties

    [ProtectedPersonalData]
    public string? Email { get; set; }

    public string? Id { get; set; }

    public string BurgeeDataUri { get; set; } = string.Empty;
    public string BurgeeFileName { get; set; } = string.Empty;

    public string? ImageId { get; set; } = null;
    public string Name { get; set; } = string.Empty;

    [ProtectedPersonalData]
    public string? Phone { get; set; }

    public bool Private { get; set; }

    public string? Website { get; set; }

    public string? AddressLine1 { get; set; }
    public string? AddressLine2 { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
    public string? Country { get; set; }

    public string? Description { get; set; }

    public bool Approved { get; set; } = false;

    #endregion

    public OrganizingAuthority ToEntity()
    {
        return new OrganizingAuthority
        {
            Email = Email,
            Id = Id!,
            ImageId = ImageId,
            Name = Name,
            Phone = Phone,
            Private = Private,
            Website = Website,
            AddressLine1 = AddressLine1,
            AddressLine2 = AddressLine2,
            City = City,
            State = State,
            PostalCode = PostalCode,
            Country = Country,
            Description = Description,
            Approved = Approved,
        };
    }

    public OrganizingAuthority ToEntity(string imageId)
    {
        return new OrganizingAuthority
        {
            Email = Email,
            Id = Id!,
            Name = Name,
            Phone = Phone,
            Private = Private,
            Website = Website,
            ImageId = imageId,
            AddressLine1 = AddressLine1,
            AddressLine2 = AddressLine2,
            City = City,
            State = State,
            PostalCode = PostalCode,
            Country = Country,
            Description = Description,
            Approved = Approved,
        };
    }

    public static OrganizingAuthorityUploadDto FromEntity(OrganizingAuthority entity)
    {
        return new OrganizingAuthorityUploadDto
        {
            Id = entity.Id,
            ImageId = entity.ImageId,
            Name = entity.Name,
            Email = entity.Email,
            Phone = entity.Phone,
            Website = entity.Website,
            Private = entity.Private,
            AddressLine1 = entity.AddressLine1,
            AddressLine2 = entity.AddressLine2,
            City = entity.City,
            State = entity.State,
            PostalCode = entity.PostalCode,
            Country = entity.Country,
            Description = entity.Description,
            Approved = entity.Approved,
        };
    }
}
