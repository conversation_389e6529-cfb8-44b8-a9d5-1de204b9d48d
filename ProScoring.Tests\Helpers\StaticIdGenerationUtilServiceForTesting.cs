using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Tests.Helpers;

/// <summary>
/// A static wrapper for the IIdGenerationUtilService to support testing scenarios.
/// </summary>
/// <remarks>
/// Because the <see cref="ApplicationDbContext"/> OnModelCreating method is only called once
/// and then Cached, we need to call tests that depend on mocking the <see cref="IIdGenerationUtilService"/>
/// serially, and use this wrapper for the <see cref="IIdGenerationUtilService"/> to ensure that the
/// they get the proper underlying IIdGenerationUtilService or mock.
/// </remarks>
public class StaticIdGenerationUtilServiceForTesting : IIdGenerationUtilService
{
    #region Fields

    private static readonly StaticIdGenerationUtilServiceForTesting _instance =
        new StaticIdGenerationUtilServiceForTesting();

    #endregion Fields

    #region Properties

    public IIdGenerationUtilService? WrappedIdGenService { get; private set; }

    #endregion Properties

    #region Constructor

    private StaticIdGenerationUtilServiceForTesting() { }

    #endregion Constructor

    #region IIdGenerationUtilService Implementation

    /// <inheritdoc/>
    public string GenerateId(IHasAutoInsertedId entity)
    {
        return WrappedIdGenService!.GenerateId(entity);
    }

    #endregion IIdGenerationUtilService Implementation

    #region Configuration Methods

    /// <summary>
    /// Configures the service with a specific IIdGenerationUtilService implementation.
    /// </summary>
    /// <param name="wrappedIdGenService">The service implementation to use.</param>
    /// <exception cref="ArgumentNullException">Thrown when wrappedIdGenService is null.</exception>
    public static void Configure(IIdGenerationUtilService wrappedIdGenService)
    {
        if (wrappedIdGenService == null)
            throw new ArgumentNullException(nameof(wrappedIdGenService));
        _instance.WrappedIdGenService = wrappedIdGenService;
    }

    /// <summary>
    /// Configures the service using a factory method.
    /// </summary>
    /// <param name="wrappedIdGenServiceFactory">Factory method to create the service implementation.</param>
    /// <exception cref="ArgumentNullException">Thrown when wrappedIdGenServiceFactory is null.</exception>
    public static void Configure(Func<IIdGenerationUtilService> wrappedIdGenServiceFactory)
    {
        if (wrappedIdGenServiceFactory == null)
            throw new ArgumentNullException(nameof(wrappedIdGenServiceFactory));
        _instance.WrappedIdGenService = wrappedIdGenServiceFactory.Invoke();
    }

    #endregion Configuration Methods

    #region Static Access Methods

    /// <summary>
    /// Gets the singleton instance of the service.
    /// </summary>
    /// <returns>The singleton instance.</returns>
    public static StaticIdGenerationUtilServiceForTesting GetInstance()
    {
        return _instance;
    }

    /// <summary>
    /// Gets the singleton instance and configures it with the provided service implementation.
    /// </summary>
    /// <param name="wrappedIdGenService">The service implementation to use.</param>
    /// <returns>The configured singleton instance.</returns>
    public static StaticIdGenerationUtilServiceForTesting GetInstanceAndConfigure(
        IIdGenerationUtilService wrappedIdGenService
    )
    {
        Configure(wrappedIdGenService);
        return _instance;
    }

    #endregion Static Access Methods
}
