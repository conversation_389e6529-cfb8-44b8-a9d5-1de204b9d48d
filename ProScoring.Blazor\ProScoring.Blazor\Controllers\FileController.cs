using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Blazor.Controllers;

/// <summary>
/// Controller for handling file operations in the ProScoring system.
/// Provides endpoints for uploading, downloading, and retrieving file records.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FileController : ControllerBase, IFileController
{
    #region Fields
    private readonly IConfiguration _configuration;
    private readonly IFileService _fileService;
    private readonly ILogger<FileController> _logger;
    #endregion Fields

    #region Constructors
    /// <summary>
    /// Initializes a new instance of the FileController class.
    /// </summary>
    /// <param name="fileService">The service for handling file operations.</param>
    /// <param name="configuration">Application configuration.</param>
    /// <param name="logger">Logger for the controller.</param>
    public FileController(IFileService fileService, IConfiguration configuration, ILogger<FileController> logger)
    {
        _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    #endregion Constructors

    #region Public API Methods
    /// <summary>
    /// Downloads a file by its ID.
    /// </summary>
    /// <param name="id">The ID of the file to download.</param>
    /// <returns>The file as a downloadable response or an error message.</returns>
    /// <response code="200">Returns the file for download.</response>
    /// <response code="404">If the file was not found.</response>
    /// <response code="500">If there was an error processing the request.</response>
    [HttpGet("download/{id}")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Download(string id)
    {
        try
        {
            var result = await _fileService.DownloadAsync(id);
            if (result == null)
            {
                _logger.LogError("Error downloading file {Id}, result was null.", id);
                return StatusCode(500, GetErrorMessage("DownloadAsync({id}) returned null."));
            }
            return File(result.Stream, result.ContentType ?? "", result.FileName);
        }
        catch (FileNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// Downloads a file as a data URI by its ID.
    /// </summary>
    /// <param name="id">The ID of the file to download.</param>
    /// <returns>The file as a data URI or an error message.</returns>
    /// <response code="200">Returns the file as a data URI.</response>
    /// <response code="404">If the file was not found.</response>
    /// <response code="500">If there was an error processing the request.</response>
    [HttpGet("downloadFileData/{id}")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DownloadFileData(string id)
    {
        try
        {
            var result = await _fileService.DownloadAsDataUriAsync(id);
            if (result == null)
            {
                _logger.LogError("Error downloading file {Id}, result was null.", id);
                return StatusCode(500, GetErrorMessage($"DownloadAsync({id}) returned null."));
            }
            return Ok(result);
        }
        catch (FileNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// Gets a file record by its ID.
    /// </summary>
    /// <param name="id">The ID of the file record to retrieve.</param>
    /// <returns>The file record or an error message.</returns>
    /// <response code="200">Returns the file record.</response>
    /// <response code="404">If the file record was not found.</response>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<FileRecord>> Get(string id)
    {
        try
        {
            return Ok(await _fileService.GetFileRecordAsync(id));
        }
        catch (FileNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// A test endpoint that returns "Hello World".
    /// </summary>
    /// <returns>A string message.</returns>
    [HttpGet("helloworld")]
    [AllowAnonymous]
    [Obsolete("This is a test endpoint and should not be used in production.")]
    public async Task<ActionResult<string>> HelloWorld()
    {
        await Task.CompletedTask;
        return Ok("Hello World");
    }

    /// <summary>
    /// A test endpoint that returns "Goodbye World".
    /// </summary>
    /// <returns>A string message.</returns>
    [HttpPost("goodbyeworld")]
    [AllowAnonymous]
    [Obsolete("This is a test endpoint and should not be used in production.")]
    public async Task<ActionResult<string>> GoodbyeWorld()
    {
        await Task.CompletedTask;
        return Ok("Goodbye World");
    }

    /// <summary>
    /// Uploads a file.
    /// </summary>
    /// <param name="file">The file to upload.</param>
    /// <param name="note">A note about the file.</param>
    /// <returns>The result of the upload operation.</returns>
    /// <response code="200">Returns the upload result if successful.</response>
    /// <response code="400">If the upload failed with a known error.</response>
    /// <response code="500">If there was an unexpected error during upload.</response>
    [HttpPost("upload")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public virtual async Task<ActionResult<FileUploadResult>> Upload(IFormFile file, [FromForm] string note)
    {
        try
        {
            var uploadResult = await _fileService.UploadAsync(file, note);

            if (uploadResult.ErrorCode != (int)FileUploadResult.ErrorCodes.None)
            {
                return BadRequest(uploadResult);
            }

            return Ok(uploadResult);
        }
        catch (Exception ex)
        {
            return HandleFileUploadError(file.Name, ex);
        }
    }

    /// <summary>
    /// Uploads a file from a data URI.
    /// </summary>
    /// <param name="fileName">The name of the file.</param>
    /// <param name="note">A note about the file.</param>
    /// <param name="fileData">The file data as a data URI.</param>
    /// <returns>The result of the upload operation.</returns>
    /// <response code="200">Returns the upload result if successful.</response>
    /// <response code="400">If the upload failed with a known error.</response>
    /// <response code="500">If there was an unexpected error during upload.</response>
    [HttpPost("uploadFileData")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public virtual async Task<ActionResult<FileUploadResult>> UploadFileData(
        string fileName,
        string note,
        string fileData
    )
    {
        try
        {
            var uploadResult = await _fileService.UploadFromDataUriAsync(fileName, note, fileData);

            if (uploadResult.ErrorCode != (int)FileUploadResult.ErrorCodes.None)
            {
                return BadRequest(uploadResult);
            }

            return Ok(uploadResult);
        }
        catch (Exception ex)
        {
            return HandleFileUploadError(fileName, ex);
        }
    }
    #endregion Public API Methods

    #region Private Methods
    /// <summary>
    /// Handles errors that occur during file upload operations.
    /// </summary>
    /// <param name="fileName">The name of the file being uploaded.</param>
    /// <param name="ex">The exception that occurred.</param>
    /// <returns>An appropriate error response.</returns>
    private ActionResult<FileUploadResult> HandleFileUploadError(string fileName, Exception ex)
    {
        _logger.LogError(ex, "Error uploading file {FileName}", fileName);
        return StatusCode(500, GetErrorMessage(ex.Message));
    }

    /// <summary>
    /// Gets an appropriate error message based on the environment.
    /// </summary>
    /// <param name="details">The detailed error message (only included in development).</param>
    /// <returns>The error message to return to the client.</returns>
    private string GetErrorMessage(string details)
    {
        var environment = _configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT");
        return environment == "Development" ? $"Internal server error: {details}" : "Internal server error.";
    }
    #endregion Private Methods
}
