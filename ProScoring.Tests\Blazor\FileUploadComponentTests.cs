using System.Text.Json;
using Bunit;
using FluentAssertions;
using Microsoft.AspNetCore.Components.Forms;
using NSubstitute;
using ProScoring.Blazor.Components.Shared;
using ProScoring.Blazor.Controllers;
using ProScoring.Domain.Dtos;
using RichardSzalay.MockHttp;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the <see cref="FileUpload"/> component.
/// </summary>
public class FileUploadComponentTests : TestContext
{
    private bool _disposed;

    #region Test Methods
    [Fact]
    public async Task FileUpload_FileSelected_UploadButtonVisible()
    {
        // Arrange
        var cut = RenderComponent<FileUpload>();
        var file = CreateMockedBrowserFile();

        // Act
        await cut.Instance.HandleFileSelection(new InputFileChangeEventArgs([file]));

        // Assert
        cut.FindAll("button:contains('Upload')").Count.Should().Be(1);
    }

    [Fact]
    public void FileUpload_InitialRender_ShowsUploadControl()
    {
        // Arrange & Act
        var cut = RenderComponent<FileUpload>();

        // Assert
        cut.FindAll("input[type='file']").Count.Should().Be(1);
        cut.Markup.Should().Contain("File Upload");
    }

    [Fact]
    public void FileUpload_NoFileSelected_UploadButtonNotVisible()
    {
        // Arrange & Act
        var cut = RenderComponent<FileUpload>();

        // Assert
        cut.FindAll("button:contains('Upload')").Count.Should().Be(0);
    }

    [Fact]
    public async Task FileUpload_ValidFile_CallsFileUploadEndpoint()
    {
        // Arrange
        var mockFileController = Substitute.For<IFileController>();
        Services.AddScoped(_ => mockFileController);
        var mockHttp = new MockHttpMessageHandler();
        var httpClient = mockHttp.ToHttpClient();
        httpClient.BaseAddress = new Uri("http://localhost");
        var cut = RenderComponent<FileUpload>();
        var file = CreateMockedBrowserFile();

        var uploadResult = new FileUploadResult
        {
            Id = "6fEE5s-3",
            TrustedFileNameForDisplay = file.Name,
            StoredFileName = Path.GetTempFileName(),
            ErrorCode = 0,
        };

        mockHttp.When("/api/file/upload").Respond("application/json", JsonSerializer.Serialize(uploadResult));

        // Act
        await cut.Instance.HandleFileSelection(new InputFileChangeEventArgs([file]));
        await cut.Instance.UploadFile();

        // Assert
        mockHttp.VerifyNoOutstandingRequest();
        mockHttp.VerifyNoOutstandingExpectation();
    }
    #endregion Test Methods

    #region IDisposable Implementation
    ~FileUploadComponentTests()
    {
        Dispose(false);
    }

    protected override void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Clean up managed resources
                base.Dispose(disposing);
            }

            _disposed = true;
        }
    }
    #endregion IDisposable Implementation

    #region Helper Methods
    private static IBrowserFile CreateMockedBrowserFile()
    {
        var fileContent = new byte[] { 1, 2, 3 };
        var file = Substitute.For<IBrowserFile>();
        file.Name.Returns("test.jpg");
        file.ContentType.Returns("image/jpeg");
        file.OpenReadStream(Arg.Any<long>(), Arg.Any<CancellationToken>()).Returns(new MemoryStream(fileContent));
        return file;
    }
    #endregion Helper Methods
}
