# Add Regatta Feature – Developer Specification

## Overview
This specification describes the full Add Regatta feature in the Regatta Administration application. It outlines requirements, data handling, validation, error strategy, and testing plans for a production-grade implementation.

---

## 🧱 High-Level Feature Description

Authorized users from a given **Organizing Authority** can create a new **Regatta** via the **Add Regatta** page. Upon creation, the user is redirected to the **Regatta Admin** page. All validation and data handling logic ensures a consistent and secure user experience.

---

## 🧭 Architecture

- **Frontend**: Hybrid Blazor using Radzen components.
- **Backend**: ASP.NET Core Web API using a layered architecture (Domain, Application, Infrastructure).
- **Testing Stack**: xUnit, FluentAssertions, NSubstitute, bUnit.

---

## 📦 API Endpoint

### `POST /api/regattas`
- **Purpose**: Creates a new Regatta for the current Organizing Authority.
- **Request Body**: A Regatta creation DTO without an `Id` (assigned by backend).
- **Response**: 
  - `201 Created` with the Regatta's ID and a Location header.
  - `400 Bad Request` with validation errors.
  - `403 Forbidden` if user lacks Organizing Authority permission.
- **Authorization**: Requires permission to create Regattas for the current Organizing Authority.
- **Routing Note**: No Organizing Authority ID in the route — determined from user context.

---

## 🧾 Fields and Form Design

### Required Fields

| Field | Type | Notes |
|-------|------|-------|
| Name | string | Required |
| Start Date | date | Calendar picker |
| End Date | date | Calendar picker |
| Competition Days | list of dates | Optional; defaults to all inclusive between Start and End |
| Entry Fee | decimal | Required |
| Late Fee | decimal | Required |
| Late Fee Date | date | Required |
| Registration Close Date | date | Required |
| Venue Name | string | Required |
| Address Line 1 | string | Optional |
| Address Line 2 | string | Optional |
| City | string | Required |
| State/Province | string | Required |
| Postal Code | string | Required |
| Country | string | Required |
| Website | string | Optional |
| Description | string | Optional |
| Logo Upload | file | Optional; follows same logic as OA burgee upload |
| External Links | list of { name, url } | Up to 10; drag-and-drop sortable |

> Tooltips are included for most fields based on internal defaults and can be edited later.

---

## 🔁 Data Handling and Validation

### Backend Validation Rules
- Name: Non-empty
- Start Date ≤ End Date
- Competition Days must fall within Start and End Dates
- Late Fee Date and Registration Close Date must be on or after Start Date
- External links: max 10 unless overridden by privileged user

### Frontend Rules (must match backend)
- Prevent form submission with invalid fields
- Calendar pickers for all date fields
- Tooltip hints on hover
- File upload interface with preview and clear options

---

## 🧰 Upload Logic

- Follows same pattern as Organizing Authority Burgee upload.
- Stored in dedicated blob container (`regattalogos`).
- Upload settings distinct from other image types (configurable).

---

## 🔀 Navigation and Flow

- Access: Only from Organizing Authority's Details page
- On success: Redirect to Regatta Admin page
- Regatta Admin page path: `/regattas/{id}/admin`

---

## 🔒 Permissions and Identity

- Create Regatta only allowed if user has `OrganizingAuthority.Manage` for the relevant OA.
- Organizing Authority ID is not provided in form or route — assigned by backend context.
- Regatta ID is assigned by backend and never included in POST request.

---

## 🧪 Testing Plan

### Unit Tests (xUnit, NSubstitute, FluentAssertions)
- [ ] DTO validation logic
- [ ] Fee logic and date constraints
- [ ] External link count and ordering logic
- [ ] Competition Days default behavior

### Integration Tests
- [ ] API route returns 201 and correct Location
- [ ] Error conditions (403, 400) for unauthorized or invalid submissions
- [ ] Logo upload endpoint and storage verification

### Frontend Tests (bUnit)
- [ ] Field visibility and default states
- [ ] Drag-and-drop link reordering
- [ ] Calendar pickers function as expected
- [ ] Successful POST redirects to admin page

---

## 📝 Additional Notes

- Organizing Authority is not included in the URL route for creation.
- External links beyond 10 can be added only by privileged users on the Edit form.
- The Create Regatta form includes dynamic help via tooltips.
- No support for Save as Draft.
- ID, CreatedBy, ModifiedBy, CreatedAt, ModifiedAt are backend-managed — do not include in POST.
