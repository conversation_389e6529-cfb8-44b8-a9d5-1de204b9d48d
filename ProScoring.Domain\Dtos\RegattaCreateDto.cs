using System.ComponentModel.DataAnnotations;
using ProScoring.Domain.Entities.RegattaEntities;

namespace ProScoring.Domain.Dtos;

/// <summary>
/// Data transfer object for creating a new regatta.
/// </summary>
public class RegattaCreateDto
{
    #region Properties

    /// <summary>
    /// Gets or sets the name of the regatta.
    /// </summary>
    [Required(ErrorMessage = "Name is required")]
    [StringLength(255, MinimumLength = 3, ErrorMessage = "Name must be between 3 and 255 characters")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the description of the regatta.
    /// </summary>
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the start date of the regatta.
    /// </summary>
    [Required(ErrorMessage = "Start date is required")]
    public DateOnly StartDate { get; set; }

    /// <summary>
    /// Gets or sets the end date of the regatta.
    /// </summary>
    [Required(ErrorMessage = "End date is required")]
    public DateOnly EndDate { get; set; }

    /// <summary>
    /// Gets or sets the competition days for the regatta.
    /// If not provided, defaults to all days between start and end dates.
    /// </summary>
    public DateOnly[]? CompetitionDays { get; set; }

    /// <summary>
    /// Gets or sets the entry fee for the regatta.
    /// </summary>
    [Required(ErrorMessage = "Entry fee is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Entry fee must be non-negative")]
    public decimal EntryFee { get; set; }

    /// <summary>
    /// Gets or sets the late fee for the regatta.
    /// </summary>
    [Required(ErrorMessage = "Late fee is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Late fee must be non-negative")]
    public decimal LateFee { get; set; }

    /// <summary>
    /// Gets or sets the late fee date for the regatta.
    /// </summary>
    [Required(ErrorMessage = "Late fee date is required")]
    public DateOnly LateFeeDate { get; set; }

    /// <summary>
    /// Gets or sets the registration close date for the regatta.
    /// </summary>
    [Required(ErrorMessage = "Registration close date is required")]
    public DateOnly RegistrationCloseDate { get; set; }

    /// <summary>
    /// Gets or sets the venue name for the regatta.
    /// </summary>
    [Required(ErrorMessage = "Venue name is required")]
    [StringLength(255, ErrorMessage = "Venue name cannot exceed 255 characters")]
    public string VenueName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the first line of the address where the regatta is held.
    /// </summary>
    [StringLength(100, ErrorMessage = "Address line 1 cannot exceed 100 characters")]
    public string? AddressLine1 { get; set; }

    /// <summary>
    /// Gets or sets the second line of the address where the regatta is held.
    /// </summary>
    [StringLength(100, ErrorMessage = "Address line 2 cannot exceed 100 characters")]
    public string? AddressLine2 { get; set; }

    /// <summary>
    /// Gets or sets the city where the regatta is held.
    /// </summary>
    [Required(ErrorMessage = "City is required")]
    [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
    public string City { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the state or province where the regatta is held.
    /// </summary>
    [Required(ErrorMessage = "State/Province is required")]
    [StringLength(50, ErrorMessage = "State/Province cannot exceed 50 characters")]
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the postal code where the regatta is held.
    /// </summary>
    [Required(ErrorMessage = "Postal code is required")]
    [StringLength(20, ErrorMessage = "Postal code cannot exceed 20 characters")]
    public string PostalCode { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the country where the regatta is held.
    /// </summary>
    [Required(ErrorMessage = "Country is required")]
    [StringLength(60, ErrorMessage = "Country cannot exceed 60 characters")]
    public string Country { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the website URL for the regatta.
    /// </summary>
    [Url(ErrorMessage = "Invalid URL. Be sure to include `http://` or `https://`")]
    public string? Website { get; set; }

    /// <summary>
    /// Gets or sets the logo file name for upload.
    /// </summary>
    public string? LogoFileName { get; set; }

    /// <summary>
    /// Gets or sets the logo data URI for upload.
    /// </summary>
    public string? LogoDataUri { get; set; }

    /// <summary>
    /// Gets or sets the external links for the regatta.
    /// </summary>
    public List<ExternalLinkDto> ExternalLinks { get; set; } = [];

    #endregion Properties

    #region Methods

    /// <summary>
    /// Converts this DTO to a Regatta entity.
    /// </summary>
    /// <param name="organizingAuthorityId">The organizing authority ID to assign.</param>
    /// <returns>A new Regatta entity.</returns>
    public Regatta ToEntity(string organizingAuthorityId)
    {
        return new Regatta
        {
            Name = Name,
            Description = Description,
            StartDate = StartDate,
            EndDate = EndDate,
            CompetitionDays = CompetitionDays ?? GenerateDefaultCompetitionDays(),
            OrganizingAuthorityId = organizingAuthorityId,
            AddressLine1 = AddressLine1 ?? string.Empty,
            AddressLine2 = AddressLine2 ?? string.Empty,
            City = City,
            State = State,
            PostalCode = PostalCode,
            Country = Country,
            Website = Website ?? string.Empty,
            Location = VenueName, // Map VenueName to Location field
            EntryFee = EntryFee,
            LateFee = LateFee,
            LateFeeDate = LateFeeDate,
            RegistrationCloseDate = RegistrationCloseDate,
        };
    }

    /// <summary>
    /// Generates default competition days (all days between start and end dates).
    /// </summary>
    /// <returns>Array of dates between start and end dates inclusive.</returns>
    private DateOnly[] GenerateDefaultCompetitionDays()
    {
        var days = new List<DateOnly>();
        var current = StartDate;

        while (current <= EndDate)
        {
            days.Add(current);
            current = current.AddDays(1);
        }

        return days.ToArray();
    }

    #endregion Methods
}

/// <summary>
/// Data transfer object for external links.
/// </summary>
public class ExternalLinkDto
{
    /// <summary>
    /// Gets or sets the link type ID.
    /// </summary>
    [Required(ErrorMessage = "Link type is required")]
    public string LinkTypeId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the URL of the external link.
    /// </summary>
    [Required(ErrorMessage = "URL is required")]
    [Url(ErrorMessage = "Invalid URL. Be sure to include `http://` or `https://`")]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the display order of the link.
    /// </summary>
    public int Order { get; set; }
}
