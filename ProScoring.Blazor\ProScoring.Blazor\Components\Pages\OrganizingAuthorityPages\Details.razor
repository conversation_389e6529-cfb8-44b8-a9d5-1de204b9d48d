@page "/organizingauthorities/details"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using ProScoring.Blazor.Extensions
@using ProScoring.BusinessLogic.ServiceInterfaces
@using ProScoring.Domain.Entities
@using ProScoring.Infrastructure.Authorization
@using Ra<PERSON>zen
@using Radzen.Blazor
@inject IOrganizingAuthorityService organizingAuthorityService
@inject NavigationManager navigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Details: @(organizingAuthority?.Name ?? "Loading...")</PageTitle>

<div class="d-flex justify-content-center align-items-start" style="min-height: 100vh; padding: 2rem;">
    <RadzenCard Style="width: 100%; max-width: 800px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 8px;">
        @if (organizingAuthority != null)
        {
            <RadzenStack Gap="1rem" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center">
                @if (!string.IsNullOrEmpty(organizingAuthority?.ImageId))
                {
                    <RadzenImage Path="@($"/api/file/download/{organizingAuthority.ImageId}")"
                        AlternateText="@($"{organizingAuthority.Name} Burgee")"
                        Style="width: 100px; height: 100px; object-fit: contain;" />
                }
                <RadzenText TextStyle="TextStyle.H3">@(organizingAuthority?.Name ?? "Loading...")</RadzenText>
            </RadzenStack>
            <hr />
            <RadzenStack Gap="1rem">
                @if (!string.IsNullOrEmpty(organizingAuthority.Website))
                {
                    <RadzenRow>
                        <RadzenColumn Size="12">
                            <RadzenLink Path="@organizingAuthority.Website" Target="_blank"
                                Text="@organizingAuthority.Website" />
                        </RadzenColumn>
                    </RadzenRow>
                }
                @if (!string.IsNullOrEmpty(organizingAuthority.Email))
                {
                    <RadzenRow>
                        <RadzenColumn Size="12">
                            <RadzenLink Path="@($"mailto:{organizingAuthority.Email}")" Text="@organizingAuthority.Email" />
                        </RadzenColumn>
                    </RadzenRow>
                }
                @if (!string.IsNullOrEmpty(organizingAuthority.Phone))
                {
                    <RadzenRow>
                        <RadzenColumn Size="12">
                            <RadzenLink Path="@($"tel:{organizingAuthority.Phone}")" Text="@organizingAuthority.Phone" />
                        </RadzenColumn>
                    </RadzenRow>
                }
                @if (!string.IsNullOrEmpty(organizingAuthority.AddressLine1) ||
                            !string.IsNullOrEmpty(organizingAuthority.City) ||
                            !string.IsNullOrEmpty(organizingAuthority.State) ||
                            !string.IsNullOrEmpty(organizingAuthority.Country))
                {
                    <RadzenRow>
                        <RadzenColumn Size="12">
                            <RadzenText>
                                @if (!string.IsNullOrEmpty(organizingAuthority.AddressLine1))
                                {
                                    @organizingAuthority.AddressLine1
                        
                                    <br />
                                }
                                @if (!string.IsNullOrEmpty(organizingAuthority.AddressLine2))
                                {
                                    @organizingAuthority.AddressLine2
                        
                                    <br />
                                }
                                @if (!string.IsNullOrEmpty(organizingAuthority.City) ||
                                                        !string.IsNullOrEmpty(organizingAuthority.State) ||
                                                        !string.IsNullOrEmpty(organizingAuthority.PostalCode))
                                {
                                    @organizingAuthority.City
                                    @(!string.IsNullOrEmpty(organizingAuthority.State) ? ", " +
                                                            organizingAuthority.State : "")
                                    @(!string.IsNullOrEmpty(organizingAuthority.PostalCode) ? " " +
                                                            organizingAuthority.PostalCode : "")
                        
                        <br />
                                                }
                                @if (!string.IsNullOrEmpty(organizingAuthority.Country))
                                {
                                    @organizingAuthority.Country
                                }
                            </RadzenText>
                        </RadzenColumn>
                    </RadzenRow>
                }
                @if (!string.IsNullOrEmpty(organizingAuthority.Description))
                {
                    <RadzenRow>
                        <RadzenColumn Size="12">
                            <RadzenText @attributes="@("oa-description".AsTestId())">@organizingAuthority.Description</RadzenText>
                        </RadzenColumn>
                    </RadzenRow>
                }

                <!-- Regattas Section -->
                <RadzenRow Class="mt-4">
                    <RadzenColumn Size="12">
                        <RadzenText TextStyle="TextStyle.H5" @attributes="@("regattas-heading".AsTestId())">Regattas</RadzenText>
                        <div class="mt-2" @attributes="@("regattas-placeholder".AsTestId())">
                            <RadzenText TextStyle="TextStyle.Body2" Style="color: #666;">No regattas available.</RadzenText>
                        </div>
                        <div class="mt-3">
                            <RadzenButton Text="Add Regatta" ButtonStyle="ButtonStyle.Primary"
                                Click="@AddRegatta" @attributes="@("add-regatta-button".AsTestId())" />
                        </div>
                    </RadzenColumn>
                </RadzenRow>
            </RadzenStack>
            <div class="d-flex justify-content-end align-items-center gap-3 mt-4">
                <RadzenButton Text="Back" ButtonStyle="ButtonStyle.Light" Click="@GoBack"
                    @attributes="@("oa-back-button".AsTestId())" />
                <AuthorizeView Policy="@EditAuthorizationForPageWithIdHandler.PolicyName">
                    <Authorized>
                        <RadzenButton Text="Edit" ButtonStyle="ButtonStyle.Primary" Click="Edit"
                            @attributes="@("edit-oa-button".AsTestId())" />
                    </Authorized>
                </AuthorizeView>
            </div>
        }
        else
        {
            <RadzenProgressBar Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" />
        }
    </RadzenCard>
</div>

@code {
    [SupplyParameterFromQuery(Name = "id")]
    public string? Id { get; set; }
    private OrganizingAuthority? organizingAuthority;
    private bool IsHmfic { get; set; }

    /// <summary>
    /// Initializes the component and loads organizing authority data.
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        // Check if the user has HMFIC rights
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        IsHmfic = authState.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true");

        if (!string.IsNullOrEmpty(Id))
        {
            organizingAuthority = await organizingAuthorityService.GetByIdAsync(Id);
        }
    }

    /// <summary>
    /// Navigates to the edit page for the current organizing authority.
    /// </summary>
    private void Edit()
    {
        navigationManager.NavigateTo($"/organizingauthorities/edit?id={organizingAuthority!.Id}");
    }

    /// <summary>
    /// Navigates back to the previous page.
    /// </summary>
    private void GoBack()
    {
        navigationManager.NavigateTo("javascript:window.history.back()");
    }

    /// <summary>
    /// Navigates to the create regatta page for the current organizing authority.
    /// </summary>
    private void AddRegatta()
    {
        navigationManager.NavigateTo($"/regattas/create?organizingAuthorityId={organizingAuthority!.Id}");
    }
}
