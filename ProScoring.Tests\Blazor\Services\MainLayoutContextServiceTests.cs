using FluentAssertions;
using NSubstitute;
using ProScoring.Blazor.Services;

namespace ProScoring.Tests.Blazor.Services;

/// <summary>
/// Tests for the <see cref="MainLayoutContextService"/> class which handles the main layout state
/// and login dialog functionality.
/// </summary>
public class MainLayoutContextServiceTests
{
    #region State Tests
    /// <summary>
    /// Tests that the initial state of LoginButtonVisible is true.
    /// </summary>
    [Fact]
    public void InitialState_LoginButtonVisible_IsTrue()
    {
        // Arrange
        var service = new MainLayoutContextService();

        // Assert
        service.LoginButtonVisible.Should().BeTrue();
    }
    #endregion State Tests

    #region Layout Update Tests
    /// <summary>
    /// Tests that UpdateLayout correctly sets LoginButtonVisible and invokes OnLayoutChanged event.
    /// </summary>
    [Fact]
    public void UpdateLayout_SetsLoginButtonVisible_And_InvokesOnLayoutChanged()
    {
        // Arrange
        var service = new MainLayoutContextService();
        var eventRaised = false;
        service.OnLayoutChanged += () => eventRaised = true;

        // Act
        service.UpdateLayout(loginButtonVisible: false);

        // Assert
        service.LoginButtonVisible.Should().BeFalse();
        eventRaised.Should().BeTrue();
    }

    /// <summary>
    /// Tests that UpdateLayout with null value preserves LoginButtonVisible but still invokes OnLayoutChanged.
    /// </summary>
    [Fact]
    public void UpdateLayout_NullValue_DoesNotChangeLoginButtonVisible_But_InvokesOnLayoutChanged()
    {
        // Arrange
        var service = new MainLayoutContextService();
        var initialLoginButtonVisible = service.LoginButtonVisible; // Should be true
        var eventRaised = false;
        service.OnLayoutChanged += () => eventRaised = true;

        // Act
        service.UpdateLayout(loginButtonVisible: null);

        // Assert
        service.LoginButtonVisible.Should().Be(initialLoginButtonVisible);
        eventRaised.Should().BeTrue();
    }

    /// <summary>
    /// Tests that UpdateLayout without parameters preserves LoginButtonVisible but invokes OnLayoutChanged.
    /// </summary>
    [Fact]
    public void UpdateLayout_NoParameters_DoesNotChangeLoginButtonVisible_But_InvokesOnLayoutChanged()
    {
        // Arrange
        var service = new MainLayoutContextService();
        var initialLoginButtonVisible = service.LoginButtonVisible; // Should be true
        var eventRaised = false;
        service.OnLayoutChanged += () => eventRaised = true;

        // Act
        service.UpdateLayout();

        // Assert
        service.LoginButtonVisible.Should().Be(initialLoginButtonVisible);
        eventRaised.Should().BeTrue();
    }
    #endregion Layout Update Tests

    #region Login Dialog Tests
    /// <summary>
    /// Tests that OpenLoginDialogAsync does nothing when no method is set.
    /// </summary>
    [Fact]
    public async Task OpenLoginDialogAsync_NoMethodSet_DoesNothing()
    {
        // Arrange
        var service = new MainLayoutContextService();

        // Act & Assert - should not throw an exception
        await service.OpenLoginDialogAsync();
    }

    /// <summary>
    /// Tests that OpenLoginDialogAsync correctly invokes the registered method with null action.
    /// </summary>
    [Fact]
    public async Task OpenLoginDialogAsync_MethodSet_InvokesRegisteredMethod_WithNullAction()
    {
        // Arrange
        var service = new MainLayoutContextService();
        var mockDialogMethod = Substitute.For<Func<Action?, Task>>();
        service.SetOpenLoginDialogMethod(mockDialogMethod);

        // Act
        await service.OpenLoginDialogAsync();

        // Assert
        await mockDialogMethod.Received(1).Invoke(null);
    }

    /// <summary>
    /// Tests that OpenLoginDialogAsync correctly invokes the registered method with a provided action.
    /// </summary>
    [Fact]
    public async Task OpenLoginDialogAsync_MethodSet_InvokesRegisteredMethod_WithProvidedAction()
    {
        // Arrange
        var service = new MainLayoutContextService();
        var mockDialogMethod = Substitute.For<Func<Action?, Task>>();
        service.SetOpenLoginDialogMethod(mockDialogMethod);
        Action sampleAction = () => { };

        // Act
        await service.OpenLoginDialogAsync(sampleAction);

        // Assert
        await mockDialogMethod.Received(1).Invoke(sampleAction);
    }
    #endregion Login Dialog Tests

    #region Event Tests
    /// <summary>
    /// Tests that OnLayoutChanged event properly notifies all subscribers.
    /// </summary>
    [Fact]
    public void OnLayoutChanged_Event_SubscribersAreNotified()
    {
        // Arrange
        var service = new MainLayoutContextService();
        var handler1Called = false;
        var handler2Called = false;

        service.OnLayoutChanged += () => handler1Called = true;
        service.OnLayoutChanged += () => handler2Called = true;

        // Act
        service.UpdateLayout(); // This should trigger the OnLayoutChanged event

        // Assert
        handler1Called.Should().BeTrue();
        handler2Called.Should().BeTrue();
    }
    #endregion Event Tests
}
