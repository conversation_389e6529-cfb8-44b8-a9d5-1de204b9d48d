using System;
using System.Linq;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.EntityInterfaces;
using Xunit;

namespace ProScoring.Tests.Domain.Entities;

/// <summary>
/// Contains unit tests for the <see cref="ApplicationUser"/> class.
/// </summary>
/// <remarks>
/// Tests validate:
/// - Constructor behavior and error handling
/// - Property validation and truncation rules
/// - Interface implementations (IHasAutoInsertedId)
/// - Error handling and exception propagation
/// - Seed data configuration and validation
/// </remarks>
public class ApplicationUserTests
{
    #region Fields

    private readonly ILogger<ApplicationUser> _logger;

    #endregion Fields

    #region Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="ApplicationUserTests"/> class.
    /// Sets up common test dependencies.
    /// </summary>
    public ApplicationUserTests()
    {
        _logger = Substitute.For<ILogger<ApplicationUser>>();
    }

    #endregion Constructors

    #region Constructor Tests

    /// <summary>
    /// Tests that the default constructor sets Id to null.
    /// </summary>
    [Fact]
    public void ApplicationUser_DefaultConstructor_SetsIdToNull()
    {
        // Arrange & Act
        var user = new ApplicationUser();

        // Assert
        user.Id.Should().BeNull();
    }

    /// <summary>
    /// Tests that the logger constructor sets Id to null.
    /// </summary>
    [Fact]
    public void ApplicationUser_LoggerConstructor_SetsIdToNull()
    {
        // Arrange & Act
        var user = new ApplicationUser(_logger);

        // Assert
        user.Id.Should().BeNull();
    }

    /// <summary>
    /// Tests that constructor exceptions are properly propagated.
    /// </summary>
    [Fact]
    public void ApplicationUser_LoggerConstructor_WhenThrowsException_ShouldPropagateException()
    {
        // Arrange
        var mockLogger = Substitute.For<ILogger<ApplicationUser>>();
        mockLogger
            .When(l =>
                l.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Throw(new InvalidOperationException("Constructor exception"));

        // Act & Assert
        Action act = () => new ApplicationUser(mockLogger);
        act.Should().Throw<InvalidOperationException>();
    }

    #endregion Constructor Tests

    #region Property Tests

    /// <summary>
    /// Tests that setting Id updates the base Id property.
    /// </summary>
    [Fact]
    public void Id_WhenSettingValue_ShouldSetBaseId()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        var expectedId = "test-id";

        // Act
        user.Id = expectedId;

        // Assert
        user.Id.Should().Be(expectedId);
    }

    /// <summary>
    /// Tests that GivenName truncates values exceeding maximum length.
    /// </summary>
    [Fact]
    public void GivenName_WhenValueExceedsMaxLength_ShouldTruncateTo50Characters()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        var longName = new string('A', 100);

        // Act
        user.GivenName = longName;

        // Assert
        user.GivenName.Should().HaveLength(50);
        user.GivenName.Should().Be(longName[..50]);
    }

    /// <summary>
    /// Tests that Surname truncates values exceeding maximum length.
    /// </summary>
    [Fact]
    public void Surname_WhenValueExceedsMaxLength_ShouldTruncateTo50Characters()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        var longName = new string('A', 100);

        // Act
        user.Surname = longName;

        // Assert
        user.Surname.Should().Be(longName.Substring(0, 50));
        user.Surname.Length.Should().Be(50);
    }

    /// <summary>
    /// Tests that Surname accepts values within maximum length.
    /// </summary>
    [Fact]
    public void Surname_WhenValueIsWithinMaxLength_ShouldSetValueAsIs()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        var name = "Doe";

        // Act
        user.Surname = name;

        // Assert
        user.Surname.Should().Be(name);
    }

    /// <summary>
    /// Tests that Surname setter exceptions are properly propagated.
    /// </summary>
    [Fact]
    public void Surname_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("Surname exception"));

        // Act & Assert
        Action act = () => user.Surname = "Doe";
        act.Should().Throw<InvalidOperationException>().WithMessage("Surname exception");
    }

    /// <summary>
    /// Tests that Name returns null when both names are null.
    /// </summary>
    [Fact]
    public void Name_WhenBothNamesAreNull_ShouldReturnNull()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act & Assert
        user.Name.Should().BeNull();
    }

    /// <summary>
    /// Tests that Name returns GivenName when only GivenName exists.
    /// </summary>
    [Fact]
    public void Name_WhenOnlyGivenNameExists_ShouldReturnGivenName()
    {
        // Arrange
        var user = new ApplicationUser(_logger) { GivenName = "John" };

        // Act & Assert
        user.Name.Should().Be("John");
    }

    /// <summary>
    /// Tests that Name returns Surname when only Surname exists.
    /// </summary>
    [Fact]
    public void Name_WhenOnlySurnameExists_ShouldReturnSurname()
    {
        // Arrange
        var user = new ApplicationUser(_logger) { Surname = "Doe" };

        // Act & Assert
        user.Name.Should().Be("Doe");
    }

    /// <summary>
    /// Tests that Name combines GivenName and Surname with a space.
    /// </summary>
    [Fact]
    public void Name_WhenBothNamesExist_ShouldReturnCombinedNameWithSpace()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        user.GivenName = "John";
        user.Surname = "Doe";

        // Act & Assert
        user.Name.Should().Be("John Doe");
    }

    /// <summary>
    /// Tests that Name getter returns null when an exception occurs.
    /// </summary>
    [Fact]
    public void Name_WhenGetterThrows_ShouldReturnNull()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();

        var user = new ApplicationUser(errorLogger);

        user.GivenName = "John";
        user.Surname = "Doe";

        // Setup logger to throw only on first call
        var callCount = 0;
        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x =>
            {
                if (callCount == 0)
                {
                    callCount++;
                    throw new InvalidOperationException("Test exception");
                }
                // Don't throw on subsequent calls
            });
        // Act & Assert
        user.Name.Should().BeNull();
    }

    /// <summary>
    /// Tests that UserName updates the base UserName property.
    /// </summary>
    [Fact]
    public void UserName_WhenSettingValue_ShouldSetBaseUserName()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.UserName = "johndoe";

        // Assert
        user.UserName.Should().Be("johndoe");
    }

    /// <summary>
    /// Tests that UserName setter exceptions are properly propagated.
    /// </summary>
    [Fact]
    public void UserName_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("UserName exception"));

        // Act & Assert
        Action act = () => user.UserName = "johndoe";
        act.Should().Throw<InvalidOperationException>().WithMessage("UserName exception");
    }

    /// <summary>
    /// Tests that Email updates the base Email property.
    /// </summary>
    [Fact]
    public void Email_WhenSettingValue_ShouldSetBaseEmail()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.Email = "<EMAIL>";

        // Assert
        user.Email.Should().Be("<EMAIL>");
    }

    /// <summary>
    /// Tests that Email setter exceptions are properly propagated.
    /// </summary>
    [Fact]
    public void Email_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("Email exception"));

        // Act & Assert
        Action act = () => user.Email = "<EMAIL>";
        act.Should().Throw<InvalidOperationException>().WithMessage("Email exception");
    }

    /// <summary>
    /// Tests that PhoneNumber updates the base PhoneNumber property.
    /// </summary>
    [Fact]
    public void PhoneNumber_WhenSettingValue_ShouldSetBasePhoneNumber()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.PhoneNumber = "1234567890";

        // Assert
        user.PhoneNumber.Should().Be("1234567890");
    }

    /// <summary>
    /// Tests that PhoneNumber setter exceptions are properly propagated.
    /// </summary>
    [Fact]
    public void PhoneNumber_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("PhoneNumber exception"));

        // Act & Assert
        Action act = () => user.PhoneNumber = "1234567890";
        act.Should().Throw<InvalidOperationException>().WithMessage("PhoneNumber exception");
    }

    /// <summary>
    /// Tests that AccessFailedCount updates the base AccessFailedCount property.
    /// </summary>
    [Fact]
    public void AccessFailedCount_WhenSettingValue_ShouldSetBaseAccessFailedCount()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.AccessFailedCount = 3;

        // Assert
        user.AccessFailedCount.Should().Be(3);
    }

    /// <summary>
    /// Tests that AccessFailedCount setter exceptions are properly propagated.
    /// </summary>
    [Fact]
    public void AccessFailedCount_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("AccessFailedCount exception"));

        // Act & Assert
        Action act = () => user.AccessFailedCount = 3;
        act.Should().Throw<InvalidOperationException>().WithMessage("AccessFailedCount exception");
    }

    #endregion Property Tests

    #region Interface Implementation Tests

    /// <summary>
    /// Tests that IdPrefix returns the correct value.
    /// </summary>
    [Fact]
    public void IdPrefix_ShouldReturnCorrectValue()
    {
        // Arrange
        var user = new ApplicationUser(_logger); // Act & Assert
        ((IHasAutoInsertedId)user).IdPrefix.Should().Be("U");
    }

    /// <summary>
    /// Tests that IdLength returns the correct value.
    /// </summary>
    [Fact]
    public void IdLength_ShouldReturnCorrectValue()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act & Assert
        ((IHasAutoInsertedId)user)
            .IdLength.Should()
            .Be(8);
    }

    /// <summary>
    /// Tests that IdPadToLength returns the correct value.
    /// </summary>
    [Fact]
    public void IdPadToLength_ShouldReturnFalse()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act & Assert
        ((IHasAutoInsertedId)user)
            .IdPadToLength.Should()
            .BeFalse();
    }

    /// <summary>
    /// Tests that CreatedById stores the provided value.
    /// </summary>
    [Fact]
    public void CreatedById_WhenSettingValue_ShouldStoreValue()
    {
        // Arrange
        var user = new ApplicationUser();

        // Act
        user.CreatedById = "*********";

        // Assert
        user.CreatedById.Should().Be("*********");
    }

    /// <summary>
    /// Tests that CreatedAt stores the provided value.
    /// </summary>
    [Fact]
    public void CreatedAt_WhenSettingValue_ShouldStoreValue()
    {
        // Arrange
        var user = new ApplicationUser();
        var dateTime = new DateTimeOffset(2023, 1, 1, 12, 0, 0, TimeSpan.Zero);

        // Act
        user.CreatedAt = dateTime;

        // Assert
        user.CreatedAt.Should().Be(dateTime);
    }

    /// <summary>
    /// Tests that UpdatedById stores the provided value.
    /// </summary>
    [Fact]
    public void UpdatedById_WhenSettingValue_ShouldStoreValue()
    {
        // Arrange
        var user = new ApplicationUser();

        // Act
        user.UpdatedById = "*********";

        // Assert
        user.UpdatedById.Should().Be("*********");
    }

    /// <summary>
    /// Tests that UpdatedAt stores the provided value.
    /// </summary>
    [Fact]
    public void UpdatedAt_WhenSettingValue_ShouldStoreValue()
    {
        // Arrange
        var user = new ApplicationUser();
        var dateTime = new DateTimeOffset(2023, 1, 1, 12, 0, 0, TimeSpan.Zero);

        // Act
        user.UpdatedAt = dateTime;

        // Assert
        user.UpdatedAt.Should().Be(dateTime);
    }

    #endregion Interface Implementation Tests

    #region SeedData Tests

    /// <summary>
    /// Tests that SeedData contains the null user with correct base properties.
    /// </summary>
    [Fact]
    public void SeedData_ShouldContainNullUser()
    {
        // Arrange & Act
        var seedData = ApplicationUser.SeedData;

        // Assert
        seedData.Should().NotBeEmpty();
        seedData.Should().HaveCount(1);

        var nullUser = seedData.First();
        nullUser.Id.Should().Be("U000000000");
        nullUser.UserName.Should().Be("<EMAIL>");
        nullUser.Email.Should().Be("<EMAIL>");
        nullUser.CreatedById.Should().Be("U000000000");
        nullUser.UpdatedById.Should().Be("U000000000");
    }

    /// <summary>
    /// Tests that the null user in SeedData has correct dates.
    /// </summary>
    [Fact]
    public void SeedData_NullUser_ShouldHaveCorrectDates()
    {
        // Arrange & Act
        var seedData = ApplicationUser.SeedData;

        // Assert
        var nullUser = seedData.First();
        nullUser.CreatedAt.Should().Be(new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero));
        nullUser.UpdatedAt.Should().Be(new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero));
    }

    /// <summary>
    /// Tests that the null user in SeedData has correct security settings.
    /// </summary>
    [Fact]
    public void SeedData_NullUser_ShouldHaveCorrectSecuritySettings()
    {
        // Arrange & Act
        var seedData = ApplicationUser.SeedData;

        // Assert
        var nullUser = seedData.First();
        nullUser.EmailConfirmed.Should().BeTrue();
        nullUser.PhoneNumberConfirmed.Should().BeFalse();
        nullUser.TwoFactorEnabled.Should().BeFalse();
        nullUser.LockoutEnabled.Should().BeTrue();
        nullUser.AccessFailedCount.Should().Be(0);
        nullUser.SecurityStamp.Should().Be("XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX");
        nullUser.ConcurrencyStamp.Should().Be("00000000-0000-0000-0000-000000000000");
    }

    /// <summary>
    /// Tests that the null user in SeedData has correct normalized values.
    /// </summary>
    [Fact]
    public void SeedData_NullUser_ShouldHaveCorrectNormalizedValues()
    {
        // Arrange & Act
        var seedData = ApplicationUser.SeedData;

        // Assert
        var nullUser = seedData.First();
        nullUser.NormalizedUserName.Should().Be("<EMAIL>");
        nullUser.NormalizedEmail.Should().Be("<EMAIL>");
    }

    #endregion SeedData Tests
}
