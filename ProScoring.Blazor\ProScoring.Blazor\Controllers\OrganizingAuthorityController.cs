using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Blazor.Controllers;

/// <summary>
/// Controller for managing organizing authorities in the ProScoring system.
/// Provides endpoints for CRUD operations, paging, and OData queries.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class OrganizingAuthorityController : ODataController
{
    #region Fields
    private readonly IProScoringAuthorizationService _authorizationService;
    private readonly IFileService _fileService;
    private readonly ILogger<OrganizingAuthorityController> _logger;
    private readonly IOrganizingAuthorityService _service;
    #endregion Fields

    #region Constructors
    /// <summary>
    /// Initializes a new instance of the OrganizingAuthorityController class.
    /// </summary>
    /// <param name="service">The organizing authority service.</param>
    /// <param name="fileService">The file service for handling files like burgee images.</param>
    /// <param name="authorizationService">The authorization service.</param>
    /// <param name="logger">The logger for this controller.</param>
    public OrganizingAuthorityController(
        IOrganizingAuthorityService service,
        IFileService fileService,
        IProScoringAuthorizationService authorizationService,
        ILogger<OrganizingAuthorityController> logger
    )
    {
        _service = service ?? throw new ArgumentNullException(nameof(service));
        _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
        _authorizationService = authorizationService ?? throw new ArgumentNullException(nameof(authorizationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    #endregion Constructors

    #region API Endpoints
    /// <summary>
    /// Creates a new organizing authority.
    /// </summary>
    /// <param name="dto">The organizing authority data.</param>
    /// <returns>201 Created with the new organizing authority if successful.</returns>
    /// <response code="201">Returns the newly created organizing authority.</response>
    /// <response code="400">If the dto is null or invalid.</response>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create([FromBody] OrganizingAuthorityUploadDto? dto)
    {
        if (dto == null)
        {
            return BadRequest();
        }

        var oa = dto.ToEntity();
        // Save the image first as a file, then create and save the OA.
        if (!string.IsNullOrWhiteSpace(dto.BurgeeDataUri) && !string.IsNullOrWhiteSpace(dto.BurgeeFileName))
        {
            try
            {
                var result = await _fileService.UploadFromDataUriAsync(
                    dto.BurgeeFileName,
                    "Burgee for " + dto.Name,
                    dto.BurgeeDataUri
                );
                // check if the call was successful, and log if not.
                if (result == null)
                {
                    _logger.LogWarning("Failed to upload image for Organizing Authority {OAName}", dto.Name);
                }
                else
                {
                    oa.ImageId = result!.Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Failed to upload image for Organizing Authority {OAName}. Continuing to create OA\r\n\tErrorMessage: ({ErrorMessage})",
                    dto.Name,
                    ex.Message
                );
            }
        }

        try
        {
            await _service.CreateAsync(oa);

            if (oa.CreatedById == null)
            {
                _logger.LogError(
                    "CreatedById is null for Organizing Authority {OAName} in {type}.{method}",
                    oa.Name,
                    nameof(OrganizingAuthorityController),
                    nameof(Create)
                );
                return BadRequest();
            }

            // and we need to give the current user the right to edit the OA.
            var ownerId = oa.CreatedById;
            await _authorizationService.CreateUserAuthActionAsync(ownerId, oa.Id!, AuthTypes.Actions.ADMIN);

            return CreatedAtAction(nameof(GetById), new { id = oa.Id }, oa);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Organizing Authority {OAName}", dto.Name);
            return BadRequest(new { message = "Failed to create organizing authority." });
        }
    }

    /// <summary>
    /// Deletes an organizing authority by its ID.
    /// </summary>
    /// <param name="id">The ID of the organizing authority to delete.</param>
    /// <returns>204 No Content if successful.</returns>
    /// <response code="204">If the organizing authority was successfully deleted.</response>
    /// <response code="404">If the organizing authority was not found.</response>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(string id)
    {
        var existing = await _service.GetByIdAsync(id);
        if (existing == null)
        {
            return NotFound();
        }

        await _service.DeleteAsync(id);
        return NoContent();
    }

    /// <summary>
    /// Gets all organizing authorities.
    /// </summary>
    /// <returns>A list of all organizing authorities.</returns>
    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await _service.GetAllAsync();
        return Ok(result);
    }

    /// <summary>
    /// Gets a paged list of organizing authority information DTOs.
    /// </summary>
    /// <param name="page">The page number (1-based).</param>
    /// <param name="pageSize">The number of items per page.</param>
    /// <param name="sortBy">The property to sort by.</param>
    /// <param name="sortOrder">The sort order (asc or desc).</param>
    /// <param name="filter">The filter string to apply.</param>
    /// <returns>A paged list of organizing authority information DTOs.</returns>
    [HttpGet("paged")]
    public async Task<IActionResult> GetPagedList(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? sortBy = null,
        [FromQuery] string? sortOrder = "asc",
        [FromQuery] string? filter = null
    )
    {
        var result = await _service.GetPagedListAsync(page, pageSize, sortBy, sortOrder, filter);
        return Ok(result);
    }

    /// <summary>
    /// Gets organizing authorities with OData query support.
    /// </summary>
    /// <returns>Organizing authorities that match the OData query.</returns>
    [HttpGet]
    [EnableQuery(MaxTop = 100, MaxExpansionDepth = 3)]
    [Route("~/odata/OrganizingAuthorities")]
    public async Task<IActionResult> GetOData()
    {
        // Get a filtered queryable directly from the service
        // This performs filtering at the database level
        var filteredQueryable = await _service.GetFilteredQueryableForODataAsync();

        // Return as IQueryable for OData to process
        return Ok(filteredQueryable);
    }

    /// <summary>
    /// Gets an organizing authority by its ID.
    /// </summary>
    /// <param name="id">The ID of the organizing authority to retrieve.</param>
    /// <returns>The organizing authority if found, otherwise a not found response.</returns>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(string id)
    {
        var result = await _service.GetByIdAsync(id);
        if (result == null)
        {
            return NotFound();
        }
        return Ok(result);
    }

    /// <summary>
    /// Updates an organizing authority.
    /// </summary>
    /// <param name="id">The ID of the organizing authority to update.</param>
    /// <param name="organizingAuthority">The updated organizing authority data.</param>
    /// <returns>No content if successful, otherwise an error.</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(string id, [FromBody] OrganizingAuthority organizingAuthority)
    {
        if (organizingAuthority == null || id != organizingAuthority.Id)
        {
            return BadRequest();
        }

        var existing = await _service.GetByIdAsync(id);
        if (existing == null)
        {
            return NotFound();
        }

        await _service.UpdateAsync(organizingAuthority);
        return NoContent();
    }

    /// <summary>
    /// Partially updates an organizing authority using OData PATCH.
    /// </summary>
    /// <param name="id">The ID of the organizing authority to update.</param>
    /// <param name="organizingAuthority">The partial organizing authority data.</param>
    /// <returns>No content if successful, otherwise an error.</returns>
    [HttpPatch]
    [Route("~/odata/OrganizingAuthorities({id})")]
    public async Task<IActionResult> Patch(string id, [FromBody] OrganizingAuthority organizingAuthority)
    {
        if (organizingAuthority == null || id != organizingAuthority.Id)
        {
            return BadRequest();
        }

        var existing = await _service.GetByIdAsync(id);
        if (existing == null)
        {
            return NotFound();
        }

        // For a true PATCH implementation, we would only update the properties that were provided
        // For now, we'll use the same update method as PUT
        await _service.UpdateAsync(organizingAuthority);
        return NoContent();
    }

    /// <summary>
    /// Gets all unique states from organizing authorities.
    /// </summary>
    /// <returns>A list of all unique states.</returns>
    /// <response code="200">Returns the list of states.</response>
    [HttpGet("states")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAllStates()
    {
        return Ok(await _service.GetAllStatesAsync());
    }

    /// <summary>
    /// Gets all unique countries from organizing authorities.
    /// </summary>
    /// <returns>A list of all unique countries.</returns>
    /// <response code="200">Returns the list of countries.</response>
    [HttpGet("countries")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAllCountries()
    {
        return Ok(await _service.GetAllCountriesAsync());
    }
    #endregion API Endpoints
}
