using System;
using FluentAssertions;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using Xunit;

namespace ProScoring.Tests.Domain.Dtos;

/// <summary>
/// Contains unit tests for the OrganizingAuthorityInfoDto class.
/// Validates mapping from entities to info DTOs and proper handling of nullable properties.
/// </summary>
public class OrganizingAuthorityInfoDtoTests
{
    #region Test Methods

    [Fact]
    public void FromEntity_AllPropertiesSet_MapsCorrectlyToDto()
    {
        // Arrange
        var entity = new OrganizingAuthority
        {
            Id = "oa-info-123",
            Name = "Info Test OA",
            Email = "<EMAIL>",
            Phone = "************",
            Website = "http://info-testoa.com",
            Private = true,
            AddressLine1 = "1 Test Address Ln",
            AddressLine2 = "Apt 1A",
            City = "Info City",
            State = "IS",
            PostalCode = "12312",
            Country = "ICY",
            Description = "Test description for info DTO mapping",
            Approved = true,
            ImageId = "img-info-456",
            CreatedAt = DateTimeOffset.UtcNow.AddDays(-10),
            CreatedById = "user-creator",
            UpdatedAt = DateTimeOffset.UtcNow.AddDays(-1),
            UpdatedById = "user-updater",
        };

        // Act
        var dto = OrganizingAuthorityInfoDto.FromEntity(entity);

        // Assert
        dto.Should().NotBeNull();
        dto.Id.Should().Be(entity.Id);
        dto.Name.Should().Be(entity.Name);
        dto.Email.Should().Be(entity.Email);
        dto.Phone.Should().Be(entity.Phone);
        dto.Website.Should().Be(entity.Website);
        dto.City.Should().Be(entity.City);
        dto.State.Should().Be(entity.State);
        dto.Country.Should().Be(entity.Country);
        dto.Description.Should().Be(entity.Description);
        dto.ImageId.Should().Be(entity.ImageId);
        dto.Private.Should().Be(entity.Private);
        dto.Approved.Should().Be(entity.Approved);
    }

    [Fact]
    public void FromEntity_SomePropertiesNull_MapsNullsCorrectlyToDto()
    {
        // Arrange
        var entity = new OrganizingAuthority
        {
            Id = "oa-info-789",
            Name = "Info OA With Nulls",
            Email = null,
            Phone = null,
            Website = null,
            Private = false,
            AddressLine1 = "789 Null St",
            City = null,
            State = null,
            Country = null,
            Description = null,
            Approved = false,
            ImageId = null,
            CreatedAt = DateTimeOffset.UtcNow,
            CreatedById = "user-another",
        };

        // Act
        var dto = OrganizingAuthorityInfoDto.FromEntity(entity);

        // Assert
        dto.Should().NotBeNull();
        dto.Id.Should().Be(entity.Id);
        dto.Name.Should().Be(entity.Name);
        dto.Email.Should().BeNull();
        dto.Phone.Should().BeNull();
        dto.Website.Should().BeNull();
        dto.City.Should().BeNull();
        dto.State.Should().BeNull();
        dto.Country.Should().BeNull();
        dto.Description.Should().BeNull();
        dto.ImageId.Should().BeNull();
        dto.Private.Should().Be(entity.Private);
        dto.Approved.Should().Be(entity.Approved);
    }

    #endregion Test Methods
}
