using System.ComponentModel.DataAnnotations;
using FluentAssertions;
using ProScoring.Blazor.ValidationAttributes;

namespace ProScoring.Tests.Blazor.ValidationAttributes;

/// <summary>
/// Tests for the OptionalPhoneAttribute validation attribute which validates phone numbers while allowing empty values.
/// </summary>
public class OptionalPhoneAttributeTests
{
    #region Fields
    private readonly OptionalPhoneAttribute _attribute = new();
    #endregion

    #region Tests
    /// <summary>
    /// Tests that null values are considered valid
    /// </summary>
    [Fact]
    public void IsValid_NullValue_ReturnsSuccess()
    {
        // Arrange
        var validationContext = new ValidationContext(new object());

        // Act
        var result = _attribute.GetValidationResult(null, validationContext);

        // Assert
        result.Should().Be(ValidationResult.Success);
    }

    /// <summary>
    /// Tests that empty strings are considered valid
    /// </summary>
    [Fact]
    public void IsValid_EmptyString_ReturnsSuccess()
    {
        // Arrange
        var validationContext = new ValidationContext(new object());

        // Act
        var result = _attribute.GetValidationResult("", validationContext);

        // Assert
        result.Should().Be(ValidationResult.Success);
    }

    /// <summary>
    /// Tests that whitespace-only strings are considered valid
    /// </summary>
    [Fact]
    public void IsValid_WhitespaceString_ReturnsSuccess()
    {
        // Arrange
        var validationContext = new ValidationContext(new object());

        // Act
        var result = _attribute.GetValidationResult("   ", validationContext);

        // Assert
        result.Should().Be(ValidationResult.Success);
    }

    /// <summary>
    /// Tests that various valid phone number formats are accepted
    /// </summary>
    /// <param name="validPhoneNumber">A valid phone number to test</param>
    [Theory]
    [InlineData("************")]
    [InlineData("****** 123 4567")]
    [InlineData("(*************")]
    [InlineData("************")]
    public void IsValid_ValidPhoneNumber_ReturnsSuccess(string validPhoneNumber)
    {
        // Arrange
        var validationContext = new ValidationContext(new object()) { DisplayName = "Phone" };

        // Act
        var result = _attribute.GetValidationResult(validPhoneNumber, validationContext);

        // Assert
        result.Should().Be(ValidationResult.Success);
    }

    /// <summary>
    /// Tests that various invalid phone number formats are rejected
    /// </summary>
    /// <param name="invalidPhoneNumber">An invalid phone number to test</param>
    [Theory]
    [InlineData("abc")]
    [InlineData("this is not a phone number")]
    [InlineData("!@#$%^&*()")]
    public void IsValid_InvalidPhoneNumber_ReturnsErrorResult(string invalidPhoneNumber)
    {
        // Arrange
        var validationContext = new ValidationContext(new object()) { DisplayName = "Phone Number" };

        // Act
        var result = _attribute.GetValidationResult(invalidPhoneNumber, validationContext);

        // Assert
        result.Should().NotBeNull();
        result.Should().NotBe(ValidationResult.Success);
        result?.ErrorMessage.Should().Be($"The {validationContext.DisplayName} field is not a valid phone number.");
    }
    #endregion
}
