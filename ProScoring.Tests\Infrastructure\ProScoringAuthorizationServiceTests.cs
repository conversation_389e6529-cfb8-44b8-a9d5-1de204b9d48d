using System.Linq.Dynamic.Core;
using System.Security.Claims;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using MockQueryable.NSubstitute;
using NSubstitute;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.Authorization.Repositories.Interfaces;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces; // Added for ITargetHierarchyService
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.Infrastructure;

/// <summary>
/// Tests for the ProScoring authorization service, validating authorization rules and user permissions.
/// This test suite covers the core authorization functionality including action creation, deletion,
/// and permission checks.
/// </summary>
public sealed class ProScoringAuthorizationServiceTests
{
    private readonly ProScoringAuthorizationService _authorizationService;
    private readonly IApplicationDbContext _mockDbContext;
    private readonly ITestOutputHelper _output;
    private readonly IUserAuthActionRepository _mockUserAuthActionRepo;
    private readonly IOverridePermissionRepository _mockOverridePermissionRepo;
    private readonly ITargetHierarchyService _mockTargetHierarchyService; // Added
    #region Constructor
    /// <summary>
    /// Initializes a new instance of the <see cref="ProScoringAuthorizationServiceTests"/> class.
    /// Sets up the test context with mocked dependencies.
    /// </summary>
    /// <param name="output">The test output helper for logging.</param>
    public ProScoringAuthorizationServiceTests(ITestOutputHelper output)
    {
        _output = output;
        _mockDbContext = Substitute.For<IApplicationDbContext>();
        _mockUserAuthActionRepo = Substitute.For<IUserAuthActionRepository>();
        _mockOverridePermissionRepo = Substitute.For<IOverridePermissionRepository>();
        _mockTargetHierarchyService = Substitute.For<ITargetHierarchyService>(); // Added

        // Setup default empty mocks for DbSets to prevent NSubstitute issues
        var emptyActionHierarchies = new List<ActionHierarchy>().AsQueryable().BuildMockDbSet();
        var emptyAuthActions = new List<AuthAction>().AsQueryable().BuildMockDbSet();
        _mockDbContext.ActionHierarchies.Returns(emptyActionHierarchies);
        _mockDbContext.AuthActions.Returns(emptyAuthActions);

        _authorizationService = new ProScoringAuthorizationService(
            _mockDbContext,
            _output.BuildSafeLoggerFor<ProScoringAuthorizationService>(),
            _mockUserAuthActionRepo,
            _mockOverridePermissionRepo,
            _mockTargetHierarchyService // Added
        );
    }
    #endregion

    #region helpers

    private static ClaimsPrincipal CreateClaimsPrincipal(string userId, bool isHmfic = false) // Added
    {
        var claims = new List<Claim> { new(ClaimTypes.NameIdentifier, userId) };
        if (isHmfic)
        {
            claims.Add(new Claim(AuthTypes.HMFIC, "true"));
        }
        var identity = new ClaimsIdentity(claims, "TestAuth");
        return new ClaimsPrincipal(identity);
    }

    /// <summary>
    /// Creates an AuthAction with the specified name.
    /// </summary>
    /// <param name="name">The name of the auth action.</param>
    /// <returns>A new AuthAction instance.</returns>
    private static AuthAction CreateAuthAction(string name)
    {
        return new AuthAction { Name = name };
    }

    /// <summary>
    /// Creates a list of AuthActions with the specified names.
    /// </summary>
    /// <param name="names">The names of the auth actions to create.</param>
    /// <returns>A list of AuthAction instances.</returns>
    private static List<AuthAction> CreateAuthActions(params string[] names)
    {
        return names.Select(CreateAuthAction).ToList();
    }
    #endregion

    #region Test Methods
    /// <summary>
    /// Tests that the ExpandActionsAsync method executes without throwing exceptions when given valid input.
    /// </summary>
    [Fact]
    public async Task ExpandActionsAsync_ExecutesWithoutException_WhenIncludeExpressionIsValid()
    {
        // Arrange
        var action1 = CreateAuthAction("Action1");
        var action2 = CreateAuthAction("Action2");
        var authActions = new List<AuthAction> { action1, action2 };

        var actionHierarchies = new List<ActionHierarchy>
        {
            new()
            {
                ParentAction = action1,
                ParentActionName = action1.Name,
                ChildAction = action2,
                ChildActionName = action2.Name,
            },
        }
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.ActionHierarchies.Returns(actionHierarchies);
        var authActionsDbSet = authActions.AsQueryable().BuildMockDbSet();
        _mockDbContext.AuthActions.Returns(authActionsDbSet);

        // Act
        var exception = await Record.ExceptionAsync(() => _authorizationService.ExpandActionsAsync([action1]));

        // Assert
        Assert.Null(exception);
    }

    /// <summary>
    /// Tests that the CreateUserAuthActionAsync method correctly creates a user authorization action.
    /// </summary>
    [Fact]
    public async Task CreateUserAuthActionAsync_CreatesUserAuthAction_WhenInputIsValid()
    {
        // Arrange
        var userId = "user1";
        var targetId = "T134";
        var action = AuthTypes.Actions.VIEW;
        var authAction = CreateAuthAction(action);
        var userAuthAction = new UserAuthAction
        {
            UserId = userId,
            TargetId = targetId,
            AuthActionName = action,
        };

        var spyAuthService = Substitute.ForPartsOf<ProScoringAuthorizationService>(
            _mockDbContext,
            _output.BuildSafeLoggerFor<ProScoringAuthorizationService>(),
            _mockUserAuthActionRepo,
            _mockOverridePermissionRepo,
            _mockTargetHierarchyService
        );

        var mockUaaDbSet = new List<UserAuthAction>().AsQueryable().BuildMockDbSet();
        var mockTargetTypesDbSet = new List<TargetType>
        {
            new() { IdPrefix = "T", Name = "TestTargetType" },
        }
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.AuthActions.FindAsync(action).Returns(authAction);
        _mockDbContext.Users.FindAsync(userId).Returns(new ApplicationUser { Id = userId });
        _mockDbContext.UserAuthActions.Returns(mockUaaDbSet);
        _mockDbContext.TargetTypes.Returns(mockTargetTypesDbSet);

        _mockDbContext.ClearReceivedCalls();

        // Act
        var result = await spyAuthService.CreateUserAuthActionAsync(userId, targetId, action);

        // Assert
        result.Should().NotBeNull();
        result.UserId.Should().Be(userId);
        result.TargetId.Should().Be(targetId);
        result.AuthActionName.Should().Be(action);

        await _mockDbContext.Received(1).AuthActions.FindAsync(action);
        await _mockDbContext.Received(1).Users.FindAsync(userId);
        // Note: GetTargetTypeAsync uses Where().OrderByDescending().ToListAsync(), not FindAsync
        // We can't verify the Where call because it's not virtual, but the test validates the method works correctly
        await spyAuthService.Received(1).GetActionAsync(userId, targetId, action);
        _mockDbContext.UserAuthActions.Received(1).Add(Arg.Is<UserAuthAction>(uaa => uaa.Equals(userAuthAction)));
        await _mockDbContext.Received(1).SaveChangesAsync(Arg.Any<CancellationToken>());
    }

    /// <summary>
    /// Tests that the CreateUserAuthActionsAsync method correctly creates multiple user authorization actions.
    /// </summary>
    [Fact]
    public async Task CreateUserAuthActionsAsync_CreatesMultipleUserAuthActions_WhenInputIsValid()
    {
        // Arrange
        var userId = "user1";
        var targetIds = new List<string> { "T1234", "T2345" };
        var action = AuthTypes.Actions.VIEW;
        var authAction = CreateAuthAction(action);
        var user = new ApplicationUser { Id = userId };

        var mockUaaDbSet = new List<UserAuthAction>().AsQueryable().BuildMockDbSet();
        var mockTargetTypesDbSet = new List<TargetType>
        {
            new() { IdPrefix = "T", Name = "TestTargetType" },
        }
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.AuthActions.FindAsync(action).Returns(authAction);
        _mockDbContext.Users.FindAsync(userId).Returns(user);
        _mockDbContext.UserAuthActions.Returns(mockUaaDbSet);
        _mockDbContext.TargetTypes.Returns(mockTargetTypesDbSet);

        var spyAuthService = Substitute.ForPartsOf<ProScoringAuthorizationService>(
            _mockDbContext,
            _output.BuildSafeLoggerFor<ProScoringAuthorizationService>(),
            _mockUserAuthActionRepo,
            _mockOverridePermissionRepo,
            _mockTargetHierarchyService
        );

        _mockDbContext.ClearReceivedCalls();

        // Act
        var result = await spyAuthService.CreateUserAuthActionsAsync(userId, targetIds, action);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(targetIds.Count);
        result.ForEach(uaa =>
        {
            uaa.UserId.Should().Be(userId);
            uaa.AuthActionName.Should().Be(action);
        });
        result.Select(uaa => uaa.TargetId).Should().BeEquivalentTo(targetIds);

        await _mockDbContext.Received(1).Users.FindAsync(userId);
        await _mockDbContext.Received(1).AuthActions.FindAsync(action);
        // Note: GetTargetTypeAsync uses Where().OrderByDescending().ToListAsync(), not FindAsync
        // We can't verify the Where call because it's not virtual, but the test validates the method works correctly
        await spyAuthService
            .Received(targetIds.Count)
            .GetActionAsync(userId, Arg.Is<string>(tId => targetIds.Contains(tId)), action);
        _mockDbContext
            .UserAuthActions.Received(1)
            .AddRange(
                Arg.Is<IEnumerable<UserAuthAction>>(userAuthActions =>
                    userAuthActions.All(userAuthAction => userAuthAction.UserId == userId)
                )
            );
        await _mockDbContext.Received(1).SaveChangesAsync(Arg.Any<CancellationToken>());
    }

    /// <summary>
    /// Tests that the DeleteUserAuthActionAsync method correctly deletes a user authorization action.
    /// </summary>
    [Fact]
    public async Task DeleteUserAuthActionAsync_DeletesUserAuthAction_WhenActionExists()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var userAuthAction = new UserAuthAction
        {
            UserId = userId,
            TargetId = targetId,
            AuthActionName = action,
        };

        _mockDbContext.UserAuthActions.FindAsync(userId, targetId, action).Returns(userAuthAction);

        // Act
        await _authorizationService.DeleteUserAuthActionAsync(userId, targetId, action);

        // Assert
        _mockDbContext.UserAuthActions.Received(1).Remove(userAuthAction);
        await _mockDbContext.Received(1).SaveChangesAsync(Arg.Any<CancellationToken>());
    }
    #endregion

    // New Test Methods for IsAuthorizedAsync
    [Fact]
    public async Task IsAuthorizedAsync_ActorIsNull_ReturnsFalse()
    {
        // Arrange
        ClaimsPrincipal? actor = null;

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor!, "target1", AuthTypes.Actions.VIEW);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsAuthorizedAsync_UserIdIsNullInClaims_ReturnsFalse()
    {
        // Arrange
        var identity = new ClaimsIdentity([], "TestAuth"); // No NameIdentifier claim
        var actor = new ClaimsPrincipal(identity);

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, "target1", AuthTypes.Actions.VIEW);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsAuthorizedAsync_HmficClaimPresent_ReturnsTrue()
    {
        // Arrange
        var actor = CreateClaimsPrincipal("user1", isHmfic: true);

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, "target1", AuthTypes.Actions.VIEW);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsAuthorizedAsync_SpecificDenyOverride_ReturnsFalse()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        var denyOverride = new OverridePermission
        {
            UserId = userId,
            TargetId = targetId,
            ActionName = action,
            IsAllowed = false,
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
        };
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult<IEnumerable<OverridePermission>>([denyOverride]));

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeFalse();
        // Verify no further checks were made
        await _mockOverridePermissionRepo
            .DidNotReceive()
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>());
        await _mockUserAuthActionRepo.DidNotReceive().GetByUserAndTargetAsync(Arg.Any<string>(), Arg.Any<string>());
    }

    [Fact]
    public async Task IsAuthorizedAsync_SpecificAllowOverride_ReturnsTrue()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        var allowOverride = new OverridePermission
        {
            UserId = userId,
            TargetId = targetId,
            ActionName = action,
            IsAllowed = true,
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
        };
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(new List<OverridePermission> { allowOverride }.AsEnumerable()));

        // Ensure no deny overrides for specific target (already covered by the above setup for this test case)


        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeTrue();
        await _mockOverridePermissionRepo
            .DidNotReceive()
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>());
        await _mockUserAuthActionRepo.DidNotReceive().GetByUserAndTargetAsync(Arg.Any<string>(), Arg.Any<string>());
    }

    [Fact]
    public async Task IsAuthorizedAsync_UniversalDenyOverride_NoSpecific_ReturnsFalse()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));

        var denyUniversalOverride = new OverridePermission
        {
            UserId = userId,
            TargetId = AuthTypes.UNIVERSAL_TARGET,
            ActionName = action,
            IsAllowed = false,
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
        };
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult<IEnumerable<OverridePermission>>([denyUniversalOverride]));

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsAuthorizedAsync_UniversalAllowOverride_NoSpecific_ReturnsTrue()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));

        var allowUniversalOverride = new OverridePermission
        {
            UserId = userId,
            TargetId = AuthTypes.UNIVERSAL_TARGET,
            ActionName = action,
            IsAllowed = true,
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
        };
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult<IEnumerable<OverridePermission>>([allowUniversalOverride]));

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsAuthorizedAsync_SpecificAllowOverridesUniversalDeny_ReturnsTrue()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        var allowSpecific = new OverridePermission
        {
            UserId = userId,
            TargetId = targetId,
            ActionName = action,
            IsAllowed = true,
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
        };
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult<IEnumerable<OverridePermission>>([allowSpecific]));

        // Universal Deny should be ignored if a specific override is found
        var denyUniversal = new OverridePermission
        {
            UserId = userId,
            TargetId = AuthTypes.UNIVERSAL_TARGET,
            ActionName = action,
            IsAllowed = false,
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
        };
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult<IEnumerable<OverridePermission>>([denyUniversal]));

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeTrue(); // Specific Allow takes precedence
        await _mockOverridePermissionRepo
            .DidNotReceive()
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>());
    }

    [Fact]
    public async Task IsAuthorizedAsync_SpecificDenyOverridesUniversalAllow_ReturnsFalse()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        var denySpecific = new OverridePermission
        {
            UserId = userId,
            TargetId = targetId,
            ActionName = action,
            IsAllowed = false,
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
        };
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult<IEnumerable<OverridePermission>>([denySpecific]));

        var allowUniversal = new OverridePermission
        {
            UserId = userId,
            TargetId = AuthTypes.UNIVERSAL_TARGET,
            ActionName = action,
            IsAllowed = true,
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(1),
        };
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult<IEnumerable<OverridePermission>>([allowUniversal]));

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeFalse(); // Specific Deny takes precedence
        await _mockOverridePermissionRepo
            .DidNotReceive()
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>());
    }

    [Fact]
    public async Task IsAuthorizedAsync_UserAuthAction_DirectSpecificGrant_ReturnsTrue()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        // No overrides
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));

        // UserAuthAction setup
        var viewAuthActionEntity = new AuthAction { Name = AuthTypes.Actions.VIEW }; // Renamed for clarity

        // Set up AuthActions for ExpandActionsAsync
        var authActions = new List<AuthAction> { viewAuthActionEntity }
            .AsQueryable()
            .BuildMockDbSet();
        _mockDbContext.AuthActions.Returns(authActions);

        var specificGrant = new UserAuthAction
        {
            UserId = userId,
            TargetId = targetId,
            AuthActionName = action,
            AuthAction = viewAuthActionEntity,
        };
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, targetId)
            .Returns(Task.FromResult<IEnumerable<UserAuthAction>>([specificGrant]));
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, AuthTypes.UNIVERSAL_TARGET)
            .Returns(Task.FromResult(Enumerable.Empty<UserAuthAction>()));

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsAuthorizedAsync_UserAuthAction_DirectUniversalGrant_ReturnsTrue()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));

        var viewAuthActionEntity = new AuthAction { Name = AuthTypes.Actions.VIEW };

        // Set up AuthActions for ExpandActionsAsync
        var authActions = new List<AuthAction> { viewAuthActionEntity }
            .AsQueryable()
            .BuildMockDbSet();
        _mockDbContext.AuthActions.Returns(authActions);

        var universalGrant = new UserAuthAction
        {
            UserId = userId,
            TargetId = AuthTypes.UNIVERSAL_TARGET,
            AuthActionName = action,
            AuthAction = viewAuthActionEntity,
        };
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, targetId)
            .Returns(Task.FromResult(Enumerable.Empty<UserAuthAction>()));
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, AuthTypes.UNIVERSAL_TARGET)
            .Returns(Task.FromResult<IEnumerable<UserAuthAction>>([universalGrant]));

        // Note: DbContext mocks are set up in constructor with default empty collections

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsAuthorizedAsync_UserAuthAction_HierarchicalGrant_ReturnsTrue()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var requestedAction = AuthTypes.Actions.VIEW;
        var grantedActionName = AuthTypes.Actions.ADMIN;
        var actor = CreateClaimsPrincipal(userId);

        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, requestedAction, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(
                userId,
                AuthTypes.UNIVERSAL_TARGET,
                requestedAction,
                Arg.Any<DateTimeOffset>()
            )
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));

        var adminAuthActionEntity = new AuthAction { Name = grantedActionName };
        var viewAuthActionEntity = new AuthAction { Name = requestedAction };

        // Set up action hierarchy: ADMIN -> VIEW
        var actionHierarchies = new List<ActionHierarchy>
        {
            new()
            {
                ParentActionName = grantedActionName,
                ChildActionName = requestedAction,
                ParentAction = adminAuthActionEntity,
                ChildAction = viewAuthActionEntity,
            },
        }
            .AsQueryable()
            .BuildMockDbSet();

        var authActions = new List<AuthAction> { adminAuthActionEntity, viewAuthActionEntity }
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.ActionHierarchies.Returns(actionHierarchies);
        _mockDbContext.AuthActions.Returns(authActions);

        var specificGrantAdmin = new UserAuthAction
        {
            UserId = userId,
            TargetId = targetId,
            AuthActionName = grantedActionName,
            AuthAction = adminAuthActionEntity,
        };
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, targetId)
            .Returns(Task.FromResult<IEnumerable<UserAuthAction>>([specificGrantAdmin]));
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, AuthTypes.UNIVERSAL_TARGET)
            .Returns(Task.FromResult(Enumerable.Empty<UserAuthAction>()));

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, requestedAction);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsAuthorizedAsync_NoPermissions_ReturnsFalse()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));

        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, targetId)
            .Returns(Task.FromResult(Enumerable.Empty<UserAuthAction>()));
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, AuthTypes.UNIVERSAL_TARGET)
            .Returns(Task.FromResult(Enumerable.Empty<UserAuthAction>()));

        // Note: DbContext mocks are set up in constructor with default empty collections

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsAuthorizedAsync_ExpiredOverride_NotUsed_PermissionGrantedByUAA()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var actor = CreateClaimsPrincipal(userId);

        // Expired Allow Override - Should be ignored by GetActive... in the repository
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, targetId, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));
        _mockOverridePermissionRepo
            .GetActiveByUserTargetAndActionAsync(userId, AuthTypes.UNIVERSAL_TARGET, action, Arg.Any<DateTimeOffset>())
            .Returns(Task.FromResult(Enumerable.Empty<OverridePermission>()));

        // Setup UserAuthActions to grant permission
        var viewAuthActionEntity = new AuthAction { Name = AuthTypes.Actions.VIEW };

        // Set up AuthActions for ExpandActionsAsync
        var authActions = new List<AuthAction> { viewAuthActionEntity }
            .AsQueryable()
            .BuildMockDbSet();
        _mockDbContext.AuthActions.Returns(authActions);

        var specificGrant = new UserAuthAction
        {
            UserId = userId,
            TargetId = targetId,
            AuthActionName = action,
            AuthAction = viewAuthActionEntity,
        };
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, targetId)
            .Returns(Task.FromResult<IEnumerable<UserAuthAction>>([specificGrant]));
        _mockUserAuthActionRepo
            .GetByUserAndTargetAsync(userId, AuthTypes.UNIVERSAL_TARGET)
            .Returns(Task.FromResult(Enumerable.Empty<UserAuthAction>()));

        // Note: DbContext mocks are set up in constructor with default empty collections

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actor, targetId, action);

        // Assert
        result.Should().BeTrue();
    }
}
