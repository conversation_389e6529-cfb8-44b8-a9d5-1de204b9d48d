using FluentAssertions;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using Xunit;

namespace ProScoring.Tests.Domain;

/// <summary>
/// Contains unit tests for verifying the behavior of OrganizingAuthority entity and DTOs.
/// </summary>
public class OrganizingAuthorityTests
{
    #region Default Value Tests
    [Fact]
    public void OrganizingAuthority_ApprovedField_DefaultsToFalse()
    {
        // Arrange & Act
        var authority = new OrganizingAuthority();

        // Assert
        authority.Approved.Should().BeFalse();
    }

    [Fact]
    public void OrganizingAuthorityUploadDto_ApprovedField_DefaultsToFalse()
    {
        // Arrange & Act
        var dto = new OrganizingAuthorityUploadDto();

        // Assert
        dto.Approved.Should().BeFalse();
    }
    #endregion

    #region Conversion Tests
    [Fact]
    public void OrganizingAuthorityUploadDto_ToEntity_SetsApprovedField()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto { Name = "Test Authority", Approved = true };

        // Act
        var entity = dto.ToEntity();

        // Assert
        entity.Approved.Should().BeTrue();
    }

    [Fact]
    public void OrganizingAuthorityUploadDto_ToEntityWithImageId_SetsApprovedField()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto { Name = "Test Authority", Approved = true };

        // Act
        var entity = dto.ToEntity("image-id");

        // Assert
        entity.Approved.Should().BeTrue();
    }
    #endregion

    #region Mapping Tests
    [Fact]
    public void OrganizingAuthorityUploadDto_FromEntity_CopiesApprovedField()
    {
        // Arrange
        var entity = new OrganizingAuthority { Name = "Test Authority", Approved = true };

        // Act
        var dto = OrganizingAuthorityUploadDto.FromEntity(entity);

        // Assert
        dto.Approved.Should().BeTrue();
    }

    [Fact]
    public void OrganizingAuthorityInfoDto_FromEntity_CopiesApprovedField()
    {
        // Arrange
        var entity = new OrganizingAuthority { Name = "Test Authority", Approved = true };

        // Act
        var dto = OrganizingAuthorityInfoDto.FromEntity(entity);

        // Assert
        dto.Approved.Should().BeTrue();
    }
    #endregion
}
