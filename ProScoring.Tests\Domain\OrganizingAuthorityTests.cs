using FluentAssertions;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using Xunit;

namespace ProScoring.Tests.Domain;

/// <summary>
/// Contains unit tests for verifying the behavior of OrganizingAuthority entity and DTOs.
/// </summary>
public class OrganizingAuthorityTests
{
    #region Default Value Tests
    [Fact]
    public void OrganizingAuthority_ApprovedField_DefaultsToFalse()
    {
        // Arrange & Act
        var authority = new OrganizingAuthority();

        // Assert
        authority.Approved.Should().BeFalse();
    }

    [Fact]
    public void OrganizingAuthorityUploadDto_ApprovedField_DefaultsToFalse()
    {
        // Arrange & Act
        var dto = new OrganizingAuthorityUploadDto();

        // Assert
        dto.Approved.Should().BeFalse();
    }
    #endregion

    #region Conversion Tests
    [Fact]
    public void OrganizingAuthorityUploadDto_ToEntity_SetsApprovedField()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto { Name = "Test Authority", Approved = true };

        // Act
        var entity = dto.ToEntity();

        // Assert
        entity.Approved.Should().BeTrue();
    }

    [Fact]
    public void OrganizingAuthorityUploadDto_ToEntityWithImageId_SetsApprovedField()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto { Name = "Test Authority", Approved = true };

        // Act
        var entity = dto.ToEntity("image-id");

        // Assert
        entity.Approved.Should().BeTrue();
    }
    #endregion

    #region Mapping Tests
    [Fact]
    public void OrganizingAuthorityUploadDto_FromEntity_CopiesApprovedField()
    {
        // Arrange
        var entity = new OrganizingAuthority { Name = "Test Authority", Approved = true };

        // Act
        var dto = OrganizingAuthorityUploadDto.FromEntity(entity);

        // Assert
        dto.Approved.Should().BeTrue();
    }

    [Fact]
    public void OrganizingAuthorityInfoDto_FromEntity_CopiesApprovedField()
    {
        // Arrange
        var entity = new OrganizingAuthority { Name = "Test Authority", Approved = true };

        // Act
        var dto = OrganizingAuthorityInfoDto.FromEntity(entity);

        // Assert
        dto.Approved.Should().BeTrue();
    }
    #endregion

    #region Description Field Tests
    [Fact]
    public void OrganizingAuthority_DescriptionField_CanBeSetAndRetrieved()
    {
        // Arrange & Act
        var authority = new OrganizingAuthority
        {
            Name = "Test Authority",
            Description = "This is a test description for the organizing authority.",
        };

        // Assert
        authority.Description.Should().Be("This is a test description for the organizing authority.");
    }

    [Fact]
    public void OrganizingAuthority_DescriptionField_CanBeNull()
    {
        // Arrange & Act
        var authority = new OrganizingAuthority { Name = "Test Authority", Description = null };

        // Assert
        authority.Description.Should().BeNull();
    }

    [Fact]
    public void OrganizingAuthorityUploadDto_DescriptionField_CanBeSetAndRetrieved()
    {
        // Arrange & Act
        var dto = new OrganizingAuthorityUploadDto
        {
            Name = "Test Authority",
            Description = "This is a test description for the DTO.",
        };

        // Assert
        dto.Description.Should().Be("This is a test description for the DTO.");
    }

    [Fact]
    public void OrganizingAuthorityUploadDto_ToEntity_MapsDescriptionCorrectly()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto
        {
            Name = "Test Authority",
            Description = "Test description mapping",
        };

        // Act
        var entity = dto.ToEntity();

        // Assert
        entity.Description.Should().Be(dto.Description);
    }

    [Fact]
    public void OrganizingAuthorityUploadDto_FromEntity_MapsDescriptionCorrectly()
    {
        // Arrange
        var entity = new OrganizingAuthority
        {
            Name = "Test Authority",
            Description = "Entity description for mapping test",
        };

        // Act
        var dto = OrganizingAuthorityUploadDto.FromEntity(entity);

        // Assert
        dto.Description.Should().Be(entity.Description);
    }

    [Fact]
    public void OrganizingAuthorityInfoDto_FromEntity_MapsDescriptionCorrectly()
    {
        // Arrange
        var entity = new OrganizingAuthority
        {
            Name = "Test Authority",
            Description = "Info DTO description mapping test",
        };

        // Act
        var dto = OrganizingAuthorityInfoDto.FromEntity(entity);

        // Assert
        dto.Description.Should().Be(entity.Description);
    }
    #endregion
}
