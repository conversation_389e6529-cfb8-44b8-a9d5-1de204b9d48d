# Regatta Specification

This document provides details about the Regatta object, associated pages, and backend components within the ProScoring application.

## 1. Regatta Object (`ProScoring.Domain/Entities/RegattaEntities/Regatta.cs`)

The `Regatta` entity represents a sailing event, encompassing one or more races scored together.

**ID Prefix:** `G`

### Properties:

| Property Name         | Data Type                       | Description                                                                 | Notes                                                                 |
|-----------------------|---------------------------------|-----------------------------------------------------------------------------|-----------------------------------------------------------------------|
| `Id`                  | `string` (max 10)               | Unique identifier for the regatta.                                          | Primary Key, uses `ID_PREFIX` "G".                                    |
| `Name`                | `string` (min 3, max 255)       | Name of the regatta.                                                        | Required.                                                             |
| `OrganizingAuthorityId` | `string`                        | ID of the organizing authority hosting the regatta.                         | Required, Foreign Key to `OrganizingAuthority`.                       |
| `OrganizingAuthority` | `OrganizingAuthority` (virtual) | Navigation property to the linked organizing authority.                     | Auto-included on query.                                               |
| `Description`         | `string` (max 1000)             | Detailed description of the regatta.                                        | Optional.                                                             |
| `StartDate`           | `DateOnly`                      | The official start date of the regatta.                                     | Required.                                                             |
| `EndDate`             | `DateOnly`                      | The official end date of the regatta.                                       | Required.                                                             |
| `CompetitionDays`     | `DateOnly[]` (max 100)          | Array of specific dates on which competitions are held.                     | Optional.                                                             |
| `RegistrationDeadline`| `DateTimeOffset?`               | Deadline for participants to register.                                      | Optional.                                                             |
| `RegistrationOpening` | `DateTimeOffset?`               | When registration opens for the regatta.                                    | Optional.                                                             |
| `AddressLine1`        | `string` (max 100)              | First line of the venue address.                                            |                                                                       |
| `AddressLine2`        | `string` (max 100)              | Second line of the venue address.                                           | Optional.                                                             |
| `City`                | `string` (max 100)              | City where the regatta is held.                                             |                                                                       |
| `State`               | `string` (max 50)               | State or province.                                                          |                                                                       |
| `PostalCode`          | `string` (max 20)               | Postal or ZIP code.                                                         |                                                                       |
| `Country`             | `string` (max 60)               | Country where the regatta is held.                                          |                                                                       |
| `Location`            | `string` (max 1000)             | Textual description of the location (e.g., "San Francisco Bay").            |                                                                       |
| `EventLogoId`         | `string?`                       | ID of the event logo file.                                                  | Optional, Foreign Key to `FileRecord`.                                |
| `EventLogo`           | `FileRecord?` (virtual)         | Navigation property to the event logo.                                      | Auto-included on query.                                               |
| `Private`             | `bool`                          | Indicates if the regatta is private (not publicly listed/accessible).       |                                                                       |
| `Website`             | `string`                        | Official website URL for the regatta.                                       | Must be a valid URL (includes `http://` or `https://`).               |
| `EntryFee`            | `decimal`                       | Standard entry fee for the regatta.                                         |                                                                       |
| `LateFee`             | `decimal`                       | Additional fee for late registration.                                       |                                                                       |
| `LateFeeDate`         | `DateOnly?`                     | Date from which the late fee applies.                                       | Optional.                                                             |
| `RegistrationCloseDate`| `DateOnly?`                    | Date when registration officially closes.                                   | Optional.                                                             |
| `ExternalLinks`       | `ICollection<RegattaExternalLink>` (virtual) | Collection of external URLs related to the regatta (e.g., NOR, SIs). | Loaded on demand.                                                     |
| `RegattaBoats`        | `ICollection<RegattaBoat>` (virtual) | Boats participating in the regatta.                                         | Auto-included on query.                                               |
| `RegattaCompetitors`  | `ICollection<RegattaCompetitor>` (virtual) | Competitors participating in the regatta.                                   | Auto-included on query.                                               |
| `RegattaClasses`      | `ICollection<RegattaClass>` (virtual) | Classes defined for the regatta.                                            | Auto-included on query.                                               |
| `RegattaFleets`       | `ICollection<RegattaFleet>` (virtual) | Fleets defined for the regatta.                                             | Auto-included on query.                                               |
| `RegattaRatings`      | `ICollection<RegattaRating>` (virtual) | Rating systems used in the regatta.                                         | Auto-included on query.                                               |
| `RegattaRatingValues` | `ICollection<RegattaRatingValue>` (virtual) | Specific rating values for boats/classes in the regatta.                  | Auto-included on query.                                               |

*Inherits from `LastChangeTrackingWithAutoInsertedIdBase` for auditing purposes (created/updated timestamps and user IDs).*

## 2. Regatta Pages (Blazor Components)

### 2.1. Create Regatta Page

*   **Route:** `/regattas/create`
*   **Purpose:** Allows authenticated users with appropriate permissions to create a new regatta event.
*   **Navigation:**
    *   Typically accessed via a "Create Regatta" button or link, possibly from a main dashboard or an Organizing Authority's management page. (Currently, no direct link to this page is obvious from other main pages, but it's directly navigable if the route is known).
    *   Breadcrumbs: `Home > Create Regatta`
*   **Form Fields:**
    *   **Basic Information:**
        *   Name (Text Input, Required, Min 3/Max 255 chars)
        *   Venue Name (Text Input, Required) - *Note: `VenueName` is in `RegattaCreateDto` but not directly in `Regatta.cs` entity. It's likely used to populate `Regatta.Location` or similar.*
        *   Description (Text Area, Max 1000 chars)
        *   Start Date (Date Picker, Required)
        *   End Date (Date Picker, Required)
        *   Website (Text Input, Valid URL)
    *   **Registration & Fees:**
        *   Entry Fee (Numeric Input, Required, Currency format)
        *   Late Fee (Numeric Input, Required, Currency format)
        *   Late Fee Date (Date Picker, Required)
        *   Registration Close Date (Date Picker, Required)
    *   **Location:**
        *   Address Line 1 (Text Input)
        *   Address Line 2 (Text Input)
        *   City (Text Input, Required)
        *   State/Province (Text Input, Required)
        *   Postal Code (Text Input, Required)
        *   Country (Text Input, Required)
    *   **Regatta Logo:**
        *   Logo Upload (File Input, Optional, Max 1MB) - Stored as `LogoDataUri` and `LogoFileName` in `RegattaCreateDto`.
*   **Actions:**
    *   **Create Regatta (Submit Button):**
        *   Validates the form data.
        *   Calls `RegattaService.CreateAsync(dto)` to persist the new regatta.
        *   On success, displays a success notification and navigates the user to the Regatta Admin page (`/regattas/{new_regatta_id}/admin`).
        *   On failure, displays an error notification.
    *   **Cancel (Button):** Navigates the user back to the home page (`/`).

### 2.2. Regatta Admin Page

*   **Route:** `/regattas/{id}/admin` (where `{id}` is the Regatta ID)
*   **Purpose:** Displays the details of a specific regatta. It currently serves as a confirmation page after creation and a placeholder for future administrative actions (like editing, managing participants, etc.).
*   **Navigation:**
    *   Accessed automatically after successfully creating a new regatta.
    *   Can be navigated to directly if the Regatta ID is known.
    *   Presumably, will be accessible from a future "Regatta List" page or an Organizing Authority's list of regattas.
    *   Breadcrumbs: `Home > Regattas > {Regatta Name} > Admin`
*   **Information Displayed (Read-only):**
    *   Regatta Name
    *   Location
    *   Start Date
    *   End Date
    *   Description (if provided)
    *   Entry Fee
    *   Late Fee
    *   Late Fee Date (if provided)
    *   Registration Close Date (if provided)
*   **Actions:**
    *   **Back to Home (Button):** Navigates to the home page (`/`).
    *   **Edit Regatta (Button):** Currently disabled ("Coming Soon").

## 3. Regatta Controller (`ProScoring.Blazor/Controllers/RegattaController.cs`)

*   **Route Prefix:** `api/Regatta`
*   **Authorization:** The entire controller is decorated with `[Authorize]`, meaning all actions require authentication.
*   **Purpose:** Handles HTTP requests related to Regatta management. It uses `IRegattaService` to interact with the business logic layer.
*   **Key Endpoints:**
    *   **`POST /api/Regatta` (`Create` method):**
        *   Accepts a `RegattaCreateDto` object in the request body.
        *   Validates the DTO.
        *   Calls `_service.CreateAsync(dto)` to create the regatta.
        *   Returns `201 Created` with the new regatta object and a location header on success.
        *   Includes specific authorization checks within the service layer, returning `403 Forbidden` if the user lacks permission.
    *   **`GET /api/Regatta/{id}` (`GetById` method):**
        *   Accepts a regatta ID in the URL.
        *   Calls `_service.GetByIdAsync(id)` to retrieve the regatta.
        *   Returns `200 OK` with the regatta object if found and accessible.
        *   Returns `404 Not Found` if the regatta doesn't exist or the user doesn't have permission to view it (implicitly handled by the service).

This controller acts as the bridge between the Blazor frontend (or other API clients) and the backend services for managing regatta data.
