using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProScoring.Domain.Entities; // For ApplicationUser
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Infrastructure.Authorization.Entities;

/// <summary>
/// Represents an explicit override for a user's permission on a specific target and action.
/// This allows granting or denying permissions irrespective of the standard role/claim-based authorization.
/// </summary>
public class OverridePermission
    : LastChangeTrackingWithAutoInsertedIdBase,
        IHasForeignKeyConfiguration<OverridePermission>
{
    #region Constants

    /// <summary>
    /// The prefix used for OverridePermission IDs.
    /// </summary>
    public const string ID_PREFIX = "OP";

    #endregion Constants

    #region Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="OverridePermission"/> class.
    /// </summary>
    public OverridePermission()
    {
        // Default constructor for EF Core
    }

    #endregion Constructors

    #region Properties

    /// <summary>
    /// Gets or sets the unique identifier for this override permission.
    /// </summary>
    [Key]
    [MaxLength(12)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    /// <summary>
    /// Gets the prefix used for generating IDs for OverridePermission.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the name of the action being overridden (e.g., "View", "Edit").
    /// Should correspond to values typically found in `AuthTypes.Actions`.
    /// </summary>
    [Required]
    [StringLength(50)] // Matches AuthAction.Name StringLength
    [Column(Order = 20)]
    public string ActionName { get; set; } = null!;

    /// <summary>
    /// Gets or sets the optional date and time when this override permission expires.
    /// A null value means the override does not expire.
    /// </summary>
    [Column(Order = 30)]
    public DateTimeOffset? ExpiresAt { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the permission is explicitly allowed (true) or denied (false).
    /// </summary>
    [Column(Order = 40)]
    public bool IsAllowed { get; set; }

    /// <summary>
    /// Gets or sets an optional reason or justification for this override.
    /// </summary>
    [StringLength(500)]
    [Column(Order = 50)]
    public string? Reason { get; set; }

    /// <summary>
    /// Gets or sets the specific type of entity this override applies to,
    /// particularly for 'Create' actions or other type-scoped permissions.
    /// Null if the override is not scoped to a specific entity type.
    /// </summary>
    [StringLength(100)] // Assuming a reasonable max length for entity type names
    [Column(Order = 60)]
    public string? ScopedEntityType { get; set; }

    /// <summary>
    /// Gets or sets the identifier of the target resource for this override.
    /// This can be a specific entity ID or a universal target like '*'.
    /// </summary>
    [Required]
    [Column(Order = 70)]
    public string TargetId { get; set; } = null!;

    /// <summary>
    /// Gets or sets the user navigation property.
    /// </summary>
    public virtual ApplicationUser User { get; set; } = null!;

    /// <summary>
    /// Gets or sets the ID of the user for whom this override applies.
    /// </summary>
    [Required]
    [ForeignKey(nameof(User))]
    [Column(Order = 80)]
    public string UserId { get; set; } = null!;

    #endregion Properties

    #region Methods

    /// <summary>
    /// Configures the foreign key relationships for the OverridePermission entity.
    /// </summary>
    /// <param name="builder">The entity type builder.</param>
    public static void ConfigureForeignKeys(
        Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder<OverridePermission> builder
    )
    {
        // Configure User relationship
        builder
            .HasOne(op => op.User)
            .WithMany()
            .HasForeignKey(op => op.UserId)
            .OnDelete(Microsoft.EntityFrameworkCore.DeleteBehavior.Cascade);
    }

    #endregion Methods
}
