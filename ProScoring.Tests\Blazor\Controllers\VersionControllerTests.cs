using System.Reflection; // Required for asserting the type of Value
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Blazor.Controllers; // Assuming this is the namespace for VersionController
using Xunit;

namespace ProScoring.Tests.Blazor.Controllers
{
    public class VersionControllerTests
    {
        [Fact]
        public void GetVersion_ReturnsVersionInformation()
        {
            // Arrange
            var substituteLogger = Substitute.For<ILogger<VersionController>>();
            var controller = new VersionController(substituteLogger);

            // Act
            var result = controller.GetVersion();

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result.As<OkObjectResult>();
            okResult.Value.Should().NotBeNull();

            // Assert that Value has a property "Version" which is a non-null string.
            // This uses reflection to check the anonymous type's property.
            var valueType = okResult.Value!.GetType();
            var versionProperty = valueType.GetProperty("Version");
            versionProperty.Should().NotBeNull("Expected a property named 'Version' on the returned object.");
            versionProperty!.PropertyType.Should().Be(typeof(string), "Property 'Version' should be a string.");

            var versionValue = versionProperty.GetValue(okResult.Value);
            versionValue.Should().NotBeNull("Version property should not be null.");
            versionValue.As<string>().Should().NotBeNullOrEmpty("Version string should not be null or empty.");

            // Ensure no error was logged (NSubstitute specific)
            // Check if LogError was called with any arguments.
            // We need to be specific about the generic type for TState if using LogError directly.
            // A more general way to check for any error-level log:
            substituteLogger
                .DidNotReceive()
                .Log(
                    LogLevel.Error,
                    Arg.Any<EventId>(),
                    Arg.Any<object>(), // Arg.Any<TState>()
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }

        // As per prompt, GetVersion_WhenExceptionOccurs_LogsErrorAndReturnsStatusCode500 is skipped
        // due to difficulties in mocking Assembly.GetExecutingAssembly() or forcing an internal exception
        // without refactoring the controller.
    }
}
