using ProScoring.Domain.Entities;

namespace ProScoring.Domain.Dtos;

/// <summary>
/// Data transfer object for organizing authority information.
/// Contains basic information about an organizing authority.
/// </summary>
public class OrganizingAuthorityInfoDto
{
    #region properties

    /// <summary>
    /// Gets or sets the unique identifier for the organizing authority.
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// Gets or sets the name of the organizing authority.
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the email address of the organizing authority.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the phone number of the organizing authority.
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Gets or sets the website URL of the organizing authority.
    /// </summary>
    public string? Website { get; set; }

    /// <summary>
    /// Gets or sets the city of the organizing authority.
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// Gets or sets the state or province of the organizing authority.
    /// </summary>
    public string? State { get; set; }

    /// <summary>
    /// Gets or sets the country of the organizing authority.
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// Gets or sets the description of the organizing authority.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the ID of the image associated with the organizing authority.
    /// </summary>
    public string? ImageId { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the organizing authority is private.
    /// </summary>
    public bool Private { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the organizing authority is approved by an administrator.
    /// </summary>
    public bool Approved { get; set; }

    #endregion

    /// <summary>
    /// Creates a new instance of the <see cref="OrganizingAuthorityInfoDto"/> class from an <see cref="OrganizingAuthority"/> entity.
    /// </summary>
    /// <param name="entity">The organizing authority entity.</param>
    /// <returns>A new instance of the <see cref="OrganizingAuthorityInfoDto"/> class.</returns>
    public static OrganizingAuthorityInfoDto FromEntity(OrganizingAuthority entity)
    {
        return new OrganizingAuthorityInfoDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Email = entity.Email,
            Phone = entity.Phone,
            Website = entity.Website,
            City = entity.City,
            State = entity.State,
            Country = entity.Country,
            Description = entity.Description,
            ImageId = entity.ImageId,
            Private = entity.Private,
            Approved = entity.Approved,
        };
    }
}
