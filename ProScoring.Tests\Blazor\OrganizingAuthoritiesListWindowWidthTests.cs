using Blazored.LocalStorage;
using Bunit;
using FluentAssertions;
using NSubstitute;
using ProScoring.Blazor.Components.Shared;
using ProScoring.Blazor.Services;
using ProScoring.Domain.Dtos;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the <see cref="OrganizingAuthoritiesList"/> component focusing on window width functionality.
/// </summary>
public class OrganizingAuthoritiesListWindowWidthTests : TestContext
{
    private readonly IOrganizingAuthorityHttpClient _mockClient;

    public OrganizingAuthoritiesListWindowWidthTests()
    {
        // Set up mock client
        _mockClient = Substitute.For<IOrganizingAuthorityHttpClient>();
        _mockClient
            .GetInfoWithODataQueryAsync(Arg.Any<string>())
            .Returns(Task.FromResult((Items: Enumerable.Empty<OrganizingAuthorityInfoDto>(), TotalCount: 0)));

        // Set up mock local storage
        var mockLocalStorage = Substitute.For<ILocalStorageService>();
        mockLocalStorage
            .GetItemAsStringAsync("organizingAuthoritiesDisplayMode")
            .Returns(ValueTask.FromResult<string?>(null));

        // Register services
        Services.AddSingleton(_mockClient);
        Services.AddSingleton(mockLocalStorage);
        Services.AddSingleton(Substitute.For<ILocalStorageServiceWithExpiration>());

        // Configure JSInterop
        JSInterop.Mode = JSRuntimeMode.Loose;
    }

    #region Test Methods
    [Fact]
    public void WindowInnerWidth_ShouldBeAccessedAsProperty_NotFunction()
    {
        // Arrange - Set up JSInterop to throw an exception for window.innerWidth as a function
        JSInterop.SetupVoid("window.addEventListener", "resize", null, "HandleWindowResize");

        // This is the correct way to mock window.innerWidth as a property
        JSInterop.Setup<int>("window.innerWidth").SetResult(1200);

        // Act & Assert - The component should render without throwing an exception
        var cut = RenderComponent<OrganizingAuthoritiesList>();
        cut.Should().NotBeNull();
    }
    #endregion Test Methods
}
