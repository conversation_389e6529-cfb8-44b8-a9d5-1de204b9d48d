using System;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services;

/// <summary>
/// Tests for the DateTimeOffsetProviderCpuTime class.
/// </summary>
public sealed class DateTimeOffsetProviderCpuTimeTests
{
    /// <summary>
    /// Tests that UtcNow returns a value close to the system's UtcNow.
    /// </summary>
    [Fact]
    public void UtcNow_ReturnsValueCloseToSystemTime()
    {
        // Arrange
        var provider = new DateTimeOffsetProviderCpuTime();
        var tolerance = TimeSpan.FromSeconds(2);

        // Act
        var providerUtcNow = provider.UtcNow;
        var systemUtcNow = DateTimeOffset.UtcNow;

        // Assert
        var difference = (providerUtcNow - systemUtcNow).Duration();
        Assert.True(
            difference < tolerance,
            $"Difference {difference} exceeded tolerance {tolerance}. Provider: {providerUtcNow}, System: {systemUtcNow}"
        );
    }
}
