using System.Reflection;
using System.Runtime.CompilerServices;

// General Information about an assembly is controlled through the following
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.
[assembly: AssemblyTitle("ProScoring.Blazor")]
[assembly: AssemblyDescription("ProScoring Blazor Web Application")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("ProScoring")]
[assembly: AssemblyCopyright("Copyright © 2024")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// Version information for an assembly consists of the following four values:
//
//      Major Version
//      Minor Version
//      Build Number
//      Revision
//
// You can specify all the values or you can default the Build and Revision Numbers
// by using the '*' as shown below:
// [assembly: AssemblyVersion("0.1.85")]
[assembly: AssemblyVersion("0.1.85")]
[assembly: AssemblyFileVersion("0.1.85")]
[assembly: AssemblyInformationalVersion("0.1.85+202506032219")]

// Make internal members visible to test projects
[assembly: InternalsVisibleTo("ProScoring.Tests")]
[assembly: InternalsVisibleTo("ProScoring.Tests.Playwright")]
