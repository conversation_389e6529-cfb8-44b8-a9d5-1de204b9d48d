using System.Collections.Generic;
using System.Threading.Tasks;
using ProScoring.Infrastructure.Authorization.Entities;

namespace ProScoring.Infrastructure.Authorization.Repositories.Interfaces
{
    /// <summary>
    /// Defines the contract for a repository managing OverridePermission entities.
    /// </summary>
    public interface IOverridePermissionRepository
    {
        /// <summary>
        /// Gets a specific OverridePermission by its ID.
        /// </summary>
        /// <param name="id">The unique identifier of the override permission.</param>
        /// <returns>The OverridePermission if found; otherwise, null.</returns>
        Task<OverridePermission?> GetByIdAsync(string id);

        /// <summary>
        /// Gets all OverridePermissions for a specific user.
        /// </summary>
        /// <param name="userId">The user's ID.</param>
        /// <returns>An enumerable collection of OverridePermissions for the user.</returns>
        Task<IEnumerable<OverridePermission>> GetByUserAsync(string userId);

        /// <summary>
        /// Gets all OverridePermissions for a specific user and target.
        /// </summary>
        /// <param name="userId">The user's ID.</param>
        /// <param name="targetId">The target's ID.</param>
        /// <returns>An enumerable collection of OverridePermissions for the user and target.</returns>
        Task<IEnumerable<OverridePermission>> GetByUserAndTargetAsync(string userId, string targetId);

        /// <summary>
        /// Gets all OverridePermissions for a specific user, target, and action.
        /// </summary>
        /// <param name="userId">The user's ID.</param>
        /// <param name="targetId">The target's ID.</param>
        /// <param name="actionName">The action's name.</param>
        /// <returns>An enumerable collection of OverridePermissions for the user, target, and action.</returns>
        Task<IEnumerable<OverridePermission>> GetByUserTargetAndActionAsync(
            string userId,
            string targetId,
            string actionName
        );

        /// <summary>
        /// Gets all active (non-expired) OverridePermissions for a specific user, target, and action at a given time.
        /// </summary>
        /// <param name="userId">The user's ID.</param>
        /// <param name="targetId">The target's ID.</param>
        /// <param name="actionName">The action's name.</param>
        /// <param name="currentTime">The current time to check against the override's ExpiryAt property.</param>
        /// <returns>An enumerable collection of active OverridePermissions.</returns>
        Task<IEnumerable<OverridePermission>> GetActiveByUserTargetAndActionAsync(
            string userId,
            string targetId,
            string actionName,
            DateTimeOffset currentTime
        );

        /// <summary>
        /// Adds a new OverridePermission to the repository.
        /// </summary>
        /// <param name="overridePermission">The OverridePermission to add.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task AddAsync(OverridePermission overridePermission);

        /// <summary>
        /// Updates an existing OverridePermission in the repository.
        /// </summary>
        /// <param name="overridePermission">The OverridePermission to update.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task UpdateAsync(OverridePermission overridePermission);

        /// <summary>
        /// Deletes an OverridePermission from the repository by its ID.
        /// </summary>
        /// <param name="id">The ID of the OverridePermission to delete.</param>
        /// <returns>A task representing the asynchronous operation. The task result contains true if deleted, false if not found.</returns>
        Task<bool> DeleteByIdAsync(string id);
    }
}
