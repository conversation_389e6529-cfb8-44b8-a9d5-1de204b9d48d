using System;
using System.Security.Claims;
using FluentAssertions;
using ProScoring.Infrastructure.Extensions;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Extensions;

/// <summary>
/// Tests for the ClaimsPrincipal extension methods.
/// These tests verify the functionality of custom extension methods
/// for working with claims and authentication.
/// </summary>
public sealed class ClaimsPrincipalExtensionsTests
{
    #region Fields
    private const string TestClaimType = "TestType";
    private const string TestClaimValue = "TestValue";
    #endregion

    #region Helper Methods
    /// <summary>
    /// Creates a ClaimsPrincipal with the specified claims for testing.
    /// </summary>
    /// <param name="claims">The claims to add to the principal.</param>
    /// <returns>A new ClaimsPrincipal with the specified claims.</returns>
    private static ClaimsPrincipal CreateUser(params Claim[] claims)
    {
        var identity = new ClaimsIdentity(claims);
        return new ClaimsPrincipal(identity);
    }
    #endregion

    #region Tests for HasClaim
    /// <summary>
    /// Tests that HasClaim returns true when the user has the exact claim.
    /// </summary>
    [Fact]
    public void HasClaim_UserHasExactClaim_ReturnsTrue()
    {
        // Arrange
        var user = CreateUser(new Claim(TestClaimType, TestClaimValue));

        // Act
        var result = user.HasClaim(TestClaimType, TestClaimValue);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// Tests that HasClaim returns true when the user has a claim with matching type but different case.
    /// </summary>
    [Fact]
    public void HasClaim_UserHasClaimDifferentCaseValue_ReturnsTrue()
    {
        // Arrange
        var user = CreateUser(new Claim(TestClaimType, TestClaimValue));

        // Act
        var result = user.HasClaimInsensitiveValue(TestClaimType, TestClaimValue.ToLower());

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// Tests that HasClaim handles null claim value correctly.
    /// </summary>
    [Fact]
    public void HasClaim_NullClaimValue_UsesStringEmpty()
    {
        // Arrange
        var user = CreateUser(new Claim(TestClaimType, string.Empty));

        // Act
        var result = user.HasClaimInsensitiveValue(TestClaimType, (string)null!);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// Tests that HasClaim throws ArgumentNullException for null claim type.
    /// </summary>
    [Fact]
    public void HasClaim_NullClaimType_ThrowsArgumentNullException()
    {
        // Arrange
        var user = CreateUser(new Claim(TestClaimType, TestClaimValue));

        // Act
        Action act = () => user.HasClaim(null!, TestClaimValue);

        // Assert
        act.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// Tests that HasClaim returns false when the claim type does not exist.
    /// </summary>
    [Fact]
    public void HasClaim_ClaimTypeDoesNotExist_ReturnsFalse()
    {
        // Arrange
        var user = CreateUser(new Claim("OtherType", TestClaimValue));

        // Act
        var result = user.HasClaim(TestClaimType, TestClaimValue);

        // Assert
        result.Should().BeFalse();
    }

    /// <summary>
    /// Tests that HasClaim returns false when the claim value does not match.
    /// </summary>
    [Fact]
    public void HasClaim_ClaimValueDoesNotMatch_ReturnsFalse()
    {
        // Arrange
        var user = CreateUser(new Claim(TestClaimType, "OtherValue"));

        // Act
        var result = user.HasClaim(TestClaimType, TestClaimValue);

        // Assert
        result.Should().BeFalse();
    }
    #endregion
}
