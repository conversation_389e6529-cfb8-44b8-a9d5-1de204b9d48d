using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Infrastructure.Exceptions;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services;

/// <summary>
/// Tests for the PostmarkEmailSenderService class to verify email sending functionality
/// and configuration validation.
/// </summary>
public class PostmarkEmailSenderServiceTests
{
    #region Fields
    private readonly ILogger<PostmarkEmailSenderService> _loggerSubstitute;
    private PostmarkEmailSenderService? _postmarkEmailSenderService;
    #endregion Fields

    public PostmarkEmailSenderServiceTests()
    {
        _loggerSubstitute = Substitute.For<ILogger<PostmarkEmailSenderService>>();
    }

    #region Test Methods
    /// <summary>
    /// Tests that attempting to send an email without an API key throws an InvalidOperationException.
    /// </summary>
    [Fact]
    public async Task SendEmailAsync_MissingApiKey_ThrowsInvalidOperationException()
    {
        // Arrange - api<PERSON>ey is null to simulate missing configuration
        SetupEmailOptions(enabled: true, apiKey: null, fromAddress: "<EMAIL>");

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _postmarkEmailSenderService!.SendEmailAsync("<EMAIL>", "Subject", "Message")
        );
        Assert.Contains("Postmark API key is not set.", exception.Message);
    }

    /// <summary>
    /// Tests that attempting to send an email without a from address throws an InvalidOperationException.
    /// </summary>
    [Fact]
    public async Task SendEmailAsync_MissingFromAddress_ThrowsInvalidOperationException()
    {
        // Arrange - fromAddress is null to simulate missing configuration
        SetupEmailOptions(enabled: true, apiKey: "test_api_key", fromAddress: null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _postmarkEmailSenderService!.SendEmailAsync("<EMAIL>", "Subject", "Message")
        );
        Assert.Contains("Postmark From address is not set.", exception.Message);
    }

    /// <summary>
    /// Tests that when the Postmark client throws an exception, it is properly caught,
    /// logged, and wrapped in an EmailException.
    /// </summary>
    [Fact]
    public async Task SendEmailAsync_PostmarkClientThrowsException_ThrowsEmailExceptionAndLogsError()
    {
        // Arrange
        SetupEmailOptions(enabled: true, apiKey: "bogus_api_key_that_will_fail", fromAddress: "<EMAIL>");
        var expectedLogMessagePart = "failed to send email";

        // Act & Assert
        var exception = await Assert.ThrowsAsync<EmailException>(
            () => _postmarkEmailSenderService!.SendEmailAsync("<EMAIL>", "Subject", "Message")
        );

        _loggerSubstitute
            .Received(1)
            .Log(
                LogLevel.Error,
                Arg.Any<EventId>(),
                Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessagePart)),
                Arg.Any<Exception>(),
                Arg.Any<Func<object, Exception?, string>>()
            );
    }
    #endregion Test Methods

    #region Helper Methods
    private void SetupEmailOptions(bool enabled, string? apiKey, string? fromAddress, string fromName = "Test App")
    {
        // Use in-memory configuration instead of complex mocking
        var configData = new Dictionary<string, string?>();

        configData[$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.Enabled)}"] = enabled.ToString();
        configData[$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromName)}"] = fromName;

        // Only add keys that are not null (to simulate missing configuration)
        if (apiKey != null)
        {
            configData[$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.ApiKey)}"] = apiKey;
        }

        if (fromAddress != null)
        {
            configData[$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromAddress)}"] = fromAddress;
        }

        var configuration = new ConfigurationBuilder().AddInMemoryCollection(configData).Build();

        _postmarkEmailSenderService = new PostmarkEmailSenderService(configuration, _loggerSubstitute);
    }
    #endregion Helper Methods
}
