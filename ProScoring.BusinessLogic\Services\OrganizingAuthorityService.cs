using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Common;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.BusinessLogic.Services;

/// <summary>
/// Service for managing organizing authorities in the ProScoring system.
/// Handles CRUD operations and authorization for organizing authorities.
/// </summary>
public class OrganizingAuthorityService : IOrganizingAuthorityService
{
    #region Fields
    private readonly IApplicationDbContext _context;
    private readonly IFileService _fileService;
    private readonly IProScoringAuthorizationService _authorizationService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ServiceAuthorizationHelper _authHelper;
    private readonly ILogger<OrganizingAuthorityService> _logger;
    #endregion Fields

    #region Constructors
    /// <summary>
    /// Initializes a new instance of the OrganizingAuthorityService class.
    /// </summary>
    public OrganizingAuthorityService(
        IApplicationDbContext context,
        IFileService fileService,
        IProScoringAuthorizationService authorizationService,
        IHttpContextAccessor httpContextAccessor,
        ServiceAuthorizationHelper authHelper,
        ILogger<OrganizingAuthorityService> logger
    )
    {
        _context = context;
        _fileService = fileService;
        _authorizationService = authorizationService;
        _httpContextAccessor = httpContextAccessor;
        _authHelper = authHelper;
        _logger = logger;
    }
    #endregion Constructors

    #region IOrganizingAuthorityService Implementation
    /// <summary>
    /// Creates a new organizing authority.
    /// </summary>
    /// <param name="organizingAuthority">The organizing authority to create.</param>
    /// <returns>The created organizing authority.</returns>
    /// <exception cref="ArgumentNullException">Thrown when organizingAuthority is null.</exception>
    /// <exception cref="InvalidOperationException">Thrown when CreatedById is null.</exception>
    public async Task<OrganizingAuthority> CreateAsync(OrganizingAuthority organizingAuthority)
    {
        ArgumentNullException.ThrowIfNull(organizingAuthority, nameof(organizingAuthority));

        _context.OrganizingAuthorities.Add(organizingAuthority);
        await _context.SaveChangesAsync();

        var ownerId = organizingAuthority.CreatedById ?? throw new InvalidOperationException("CreatedById is null");
        await _authorizationService.CreateUserAuthActionAsync(
            ownerId,
            organizingAuthority.Id!,
            AuthTypes.Actions.ADMIN
        );

        return organizingAuthority;
    }

    /// <summary>
    /// Creates a new organizing authority from a DTO, including image upload handling.
    /// </summary>
    /// <param name="dto">The DTO containing organizing authority data.</param>
    /// <returns>The created organizing authority.</returns>
    /// <exception cref="ArgumentNullException">Thrown when dto is null.</exception>
    /// <exception cref="InvalidOperationException">Thrown when CreatedById is null or when saving fails.</exception>
    public async Task<OrganizingAuthority> CreateAsync(OrganizingAuthorityUploadDto dto)
    {
        ArgumentNullException.ThrowIfNull(dto, nameof(dto));

        var oa = dto.ToEntity();

        if (!string.IsNullOrWhiteSpace(dto.BurgeeDataUri) && !string.IsNullOrWhiteSpace(dto.BurgeeFileName))
        {
            try
            {
                var result = await _fileService.UploadFromDataUriAsync(
                    dto.BurgeeFileName,
                    "Burgee for " + dto.Name,
                    dto.BurgeeDataUri
                );
                if (result == null)
                {
                    _logger.LogWarning("Failed to upload image for Organizing Authority {OAName}", dto.Name);
                }
                else
                {
                    oa.ImageId = result.Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Failed to upload image for Organizing Authority {OAName}. Continuing to create OA\r\n\tErrorMessage: ({ErrorMessage})",
                    dto.Name,
                    ex.Message
                );
            }
        }

        try
        {
            _context.OrganizingAuthorities.Add(oa);
            await _context.SaveChangesAsync();

            if (oa.CreatedById == null)
            {
                throw new InvalidOperationException("CreatedById is null");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving Organizing Authority {OAName}. Not saving to db.", oa.Name);
            _context.Entry(oa).State = EntityState.Detached;
            throw new InvalidOperationException($"Error occurred while saving Organizing Authority {oa.Name}", ex);
        }

        var ownerId = oa.CreatedById ?? throw new InvalidOperationException("CreatedById is null");
        await _authorizationService.CreateUserAuthActionAsync(ownerId, oa.Id!, AuthTypes.Actions.ADMIN);

        return oa;
    }

    public async Task DeleteAsync(string id)
    {
        var authority = await _context.OrganizingAuthorities.FindAsync(id);
        if (authority == null)
        {
            throw new KeyNotFoundException($"Organizing Authority with id {id} not found");
        }

        // Explicitly perform the authorization check that will get logged
        await _authHelper.AuthorizeAsync(DeleteAuthorizationForResourceHandler.PolicyName, authority);

        _context.OrganizingAuthorities.Remove(authority);
        await _context.SaveChangesAsync();
    }

    public async Task<IEnumerable<OrganizingAuthority>> GetAllAsync()
    {
        return await _context.OrganizingAuthorities.ToListAsync();
    }

    public async Task<OrganizingAuthority?> GetByIdAsync(string id)
    {
        return await _context.OrganizingAuthorities.FindAsync(id);
    }

    /// <summary>
    /// Gets a paged list of organizing authority information DTOs.
    /// </summary>
    /// <param name="page">The page number (1-based).</param>
    /// <param name="pageSize">The number of items per page.</param>
    /// <param name="sortBy">The property to sort by.</param>
    /// <param name="sortOrder">The sort order (asc or desc).</param>
    /// <param name="filter">The filter string to apply.</param>
    /// <returns>A paged list of organizing authority information DTOs.</returns>
    public async Task<PagedList<OrganizingAuthorityInfoDto>> GetPagedListAsync(
        int page = 1,
        int pageSize = 10,
        string? sortBy = null,
        string? sortOrder = "asc",
        string? filter = null
    )
    {
        // Validate parameters
        page = Math.Max(1, page);
        pageSize = Math.Clamp(pageSize, 1, 100);

        // Get current user ID for authorization check
        var currentUserId = _httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
        var isAuthenticated = !string.IsNullOrEmpty(currentUserId);

        // Start with a queryable of all authorities
        IQueryable<OrganizingAuthority> query = _context.OrganizingAuthorities;

        // Apply filtering at the database level
        if (!string.IsNullOrWhiteSpace(filter))
        {
            query = query.Where(oa =>
                oa.Name.Contains(filter)
                || (oa.Email != null && oa.Email.Contains(filter))
                || (oa.Phone != null && oa.Phone.Contains(filter))
                || (oa.Website != null && oa.Website.Contains(filter))
                || (oa.City != null && oa.City.Contains(filter))
                || (oa.State != null && oa.State.Contains(filter))
                || (oa.Country != null && oa.Country.Contains(filter))
            );
        }

        // Apply access control for private organizing authorities at the database level
        if (isAuthenticated)
        {
            // Check if user has HMFIC claim which grants all permissions
            var isHmfic =
                _httpContextAccessor.HttpContext?.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true")
                ?? false;

            if (!isHmfic)
            {
                // For regular authenticated users, filter based on permissions
                // Get the IDs of authorities the user has access to
                var authorizedOaIds = await _context
                    .UserAuthActions.Where(uaa =>
                        uaa.UserId == currentUserId
                        && (
                            uaa.AuthActionName == AuthTypes.Actions.VIEW
                            || uaa.AuthActionName == AuthTypes.Actions.EDIT
                            || uaa.AuthActionName == AuthTypes.Actions.ADMIN
                        )
                    )
                    .Select(uaa => uaa.TargetId)
                    .ToListAsync();

                // Filter to only include:
                // 1. Public OAs that are approved, OR
                // 2. OAs the user has explicit access to (regardless of approval status), OR
                // 3. OAs created by the current user (regardless of approval status)
                query = query.Where(oa =>
                    (!oa.Private && oa.Approved) || authorizedOaIds.Contains(oa.Id!) || oa.CreatedById == currentUserId
                );
            }
            // If HMFIC, no filtering needed - they can see all
        }
        else
        {
            // For unauthenticated users, only show public OAs that are approved
            query = query.Where(oa => !oa.Private && oa.Approved);
        }

        // Apply sorting at the database level
        if (!string.IsNullOrWhiteSpace(sortBy))
        {
            var sortByLower = sortBy.ToLowerInvariant();
            var isDescending = sortOrder?.ToLowerInvariant() == "desc";

            query = sortByLower switch
            {
                "name" => isDescending ? query.OrderByDescending(oa => oa.Name) : query.OrderBy(oa => oa.Name),
                "email" => isDescending ? query.OrderByDescending(oa => oa.Email) : query.OrderBy(oa => oa.Email),
                "phone" => isDescending ? query.OrderByDescending(oa => oa.Phone) : query.OrderBy(oa => oa.Phone),
                "website" => isDescending ? query.OrderByDescending(oa => oa.Website) : query.OrderBy(oa => oa.Website),
                "city" => isDescending ? query.OrderByDescending(oa => oa.City) : query.OrderBy(oa => oa.City),
                "state" => isDescending ? query.OrderByDescending(oa => oa.State) : query.OrderBy(oa => oa.State),
                "country" => isDescending ? query.OrderByDescending(oa => oa.Country) : query.OrderBy(oa => oa.Country),
                _ => isDescending ? query.OrderByDescending(oa => oa.Name) : query.OrderBy(oa => oa.Name), // Default sort by name
            };
        }
        else
        {
            // Default sort by name ascending
            query = query.OrderBy(oa => oa.Name);
        }

        // Create a projection to convert entities to DTOs at the database level
        var dtoQuery = query.Select(oa => new OrganizingAuthorityInfoDto
        {
            Id = oa.Id,
            Name = oa.Name,
            Email = oa.Email,
            Phone = oa.Phone,
            Website = oa.Website,
            City = oa.City,
            State = oa.State,
            Country = oa.Country,
            ImageId = oa.ImageId,
            Private = oa.Private,
            Approved = oa.Approved,
        });

        // Use the PagedList.CreateAsync method to efficiently create the paged result
        return await PagedList<OrganizingAuthorityInfoDto>.CreateAsync(dtoQuery, page, pageSize);
    }

    public async Task<OrganizingAuthority> UpdateAsync(OrganizingAuthority organizingAuthority)
    {
        // Explicitly perform the authorization check that will get logged
        await _authHelper.AuthorizeAsync(EditAuthorizationForResourceHandler.PolicyName, organizingAuthority);

        _context.OrganizingAuthorities.Update(organizingAuthority);
        await _context.SaveChangesAsync();
        return organizingAuthority;
    }

    /// <summary>
    /// Updates an organizing authority with data from the provided DTO.
    /// </summary>
    /// <param name="dto">The DTO containing updated organizing authority data.</param>
    /// <returns>The updated organizing authority.</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the organizing authority is not found.</exception>
    /// <exception cref="UnauthorizedAccessException">Thrown when the user is not authorized to edit the organizing authority.</exception>
    public async Task<OrganizingAuthority> UpdateAsync(OrganizingAuthorityUploadDto dto)
    {
        ArgumentNullException.ThrowIfNull(dto, nameof(dto));

        // Perform authorization check
        await _authHelper.AuthorizeAsync(EditAuthorizationForResourceHandler.PolicyName, dto);

        // Log the update attempt
        var userId = _httpContextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "unknown";
        _logger.LogInformation(
            "User {UserId} updating organizing authority {OaId} ({OaName})",
            userId,
            dto.Id,
            dto.Name
        );

        var existing =
            await _context.OrganizingAuthorities.FindAsync(dto.Id)
            ?? throw new KeyNotFoundException($"Organizing Authority with id {dto.Id} not found");

        // Handle image upload if provided
        if (!string.IsNullOrWhiteSpace(dto.BurgeeDataUri) && !string.IsNullOrWhiteSpace(dto.BurgeeFileName))
        {
            try
            {
                var result = await _fileService.UploadFromDataUriAsync(
                    dto.BurgeeFileName,
                    "Burgee for " + dto.Name,
                    dto.BurgeeDataUri
                );
                if (result != null)
                {
                    existing.ImageId = result.Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Failed to upload new image for Organizing Authority {OAName}. Continuing with update\r\n\tErrorMessage: ({ErrorMessage})",
                    dto.Name,
                    ex.Message
                );
            }
        }
        else
        {
            existing.ImageId = dto.ImageId;
        }

        // Update other properties
        existing.Name = dto.Name;
        existing.Website = dto.Website;
        existing.Email = dto.Email;
        existing.Phone = dto.Phone;
        existing.Private = dto.Private;
        existing.AddressLine1 = dto.AddressLine1;
        existing.AddressLine2 = dto.AddressLine2;
        existing.City = dto.City;
        existing.State = dto.State;
        existing.PostalCode = dto.PostalCode;
        existing.Country = dto.Country;
        existing.Approved = dto.Approved;

        await _context.SaveChangesAsync();
        return existing;
    }

    /// <summary>
    /// Gets all unique states from organizing authorities, filtered by user permissions.
    /// </summary>
    /// <returns>A list of all unique states.</returns>
    public async Task<List<string>> GetAllStatesAsync()
    {
        // Get current user ID for authorization check
        var currentUserId = _httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
        var isAuthenticated = !string.IsNullOrEmpty(currentUserId);

        // Start with a queryable of all authorities
        IQueryable<OrganizingAuthority> query = _context.OrganizingAuthorities;

        // Apply access control filtering
        if (isAuthenticated)
        {
            // Check if user has HMFIC claim which grants all permissions
            var isHmfic =
                _httpContextAccessor.HttpContext?.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true")
                ?? false;

            if (!isHmfic)
            {
                // For regular authenticated users, filter based on permissions
                // Get the IDs of authorities the user has access to
                var authorizedOaIds = await _context
                    .UserAuthActions.Where(uaa =>
                        uaa.UserId == currentUserId
                        && (
                            uaa.AuthActionName == AuthTypes.Actions.VIEW
                            || uaa.AuthActionName == AuthTypes.Actions.EDIT
                            || uaa.AuthActionName == AuthTypes.Actions.ADMIN
                        )
                    )
                    .Select(uaa => uaa.TargetId)
                    .ToListAsync();

                // Filter to only include:
                // 1. Public OAs that are approved, OR
                // 2. OAs the user has explicit access to (regardless of approval status), OR
                // 3. OAs created by the current user (regardless of approval status)
                query = query.Where(oa =>
                    (!oa.Private && oa.Approved) || authorizedOaIds.Contains(oa.Id!) || oa.CreatedById == currentUserId
                );
            }
            // If HMFIC, no filtering needed - they can see all
        }
        else
        {
            // For unauthenticated users, only show public OAs that are approved
            query = query.Where(oa => !oa.Private && oa.Approved);
        }

        // Extract unique states directly from the database
        return await query
            .Where(oa => !string.IsNullOrEmpty(oa.State))
            .Select(oa => oa.State!)
            .Distinct()
            .OrderBy(s => s)
            .ToListAsync();
    }

    /// <summary>
    /// Gets all unique countries from organizing authorities, filtered by user permissions.
    /// </summary>
    /// <returns>A list of all unique countries.</returns>
    public async Task<List<string>> GetAllCountriesAsync()
    {
        // Get current user ID for authorization check
        var currentUserId = _httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
        var isAuthenticated = !string.IsNullOrEmpty(currentUserId);

        // Start with a queryable of all authorities
        IQueryable<OrganizingAuthority> query = _context.OrganizingAuthorities;

        // Apply access control filtering
        if (isAuthenticated)
        {
            // Check if user has HMFIC claim which grants all permissions
            var isHmfic =
                _httpContextAccessor.HttpContext?.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true")
                ?? false;

            if (!isHmfic)
            {
                // For regular authenticated users, filter based on permissions
                // Get the IDs of authorities the user has access to
                var authorizedOaIds = await _context
                    .UserAuthActions.Where(uaa =>
                        uaa.UserId == currentUserId
                        && (
                            uaa.AuthActionName == AuthTypes.Actions.VIEW
                            || uaa.AuthActionName == AuthTypes.Actions.EDIT
                            || uaa.AuthActionName == AuthTypes.Actions.ADMIN
                        )
                    )
                    .Select(uaa => uaa.TargetId)
                    .ToListAsync();

                // Filter to only include:
                // 1. Public OAs that are approved, OR
                // 2. OAs the user has explicit access to (regardless of approval status), OR
                // 3. OAs created by the current user (regardless of approval status)
                query = query.Where(oa =>
                    (!oa.Private && oa.Approved) || authorizedOaIds.Contains(oa.Id!) || oa.CreatedById == currentUserId
                );
            }
            // If HMFIC, no filtering needed - they can see all
        }
        else
        {
            // For unauthenticated users, only show public OAs that are approved
            query = query.Where(oa => !oa.Private && oa.Approved);
        }

        // Extract unique countries directly from the database
        return await query
            .Where(oa => !string.IsNullOrEmpty(oa.Country))
            .Select(oa => oa.Country!)
            .Distinct()
            .OrderBy(c => c)
            .ToListAsync();
    }

    /// <summary>
    /// Gets a queryable of organizing authorities filtered by user permissions for OData queries.
    /// </summary>
    /// <returns>A queryable of organizing authorities filtered by user permissions.</returns>
    public async Task<IQueryable<OrganizingAuthority>> GetFilteredQueryableForODataAsync()
    {
        // Get current user ID for authorization check
        var currentUserId = _httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
        var isAuthenticated = !string.IsNullOrEmpty(currentUserId);

        // Start with a queryable of all authorities
        IQueryable<OrganizingAuthority> query = _context.OrganizingAuthorities;

        // Apply access control filtering
        if (isAuthenticated)
        {
            // Check if user has HMFIC claim which grants all permissions
            var isHmfic =
                _httpContextAccessor.HttpContext?.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true")
                ?? false;

            if (!isHmfic)
            {
                // For regular authenticated users, filter based on permissions
                // Get the IDs of authorities the user has access to
                var authorizedOaIds = await _context
                    .UserAuthActions.Where(uaa =>
                        uaa.UserId == currentUserId
                        && (
                            uaa.AuthActionName == AuthTypes.Actions.VIEW
                            || uaa.AuthActionName == AuthTypes.Actions.EDIT
                            || uaa.AuthActionName == AuthTypes.Actions.ADMIN
                        )
                    )
                    .Select(uaa => uaa.TargetId)
                    .ToListAsync();

                // Filter to only include:
                // 1. Public OAs that are approved, OR
                // 2. OAs the user has explicit access to (regardless of approval status), OR
                // 3. OAs created by the current user (regardless of approval status)
                query = query.Where(oa =>
                    (!oa.Private && oa.Approved) || authorizedOaIds.Contains(oa.Id!) || oa.CreatedById == currentUserId
                );
            }
            // If HMFIC, no filtering needed - they can see all
        }
        else
        {
            // For unauthenticated users, only show public OAs that are approved
            query = query.Where(oa => !oa.Private && oa.Approved);
        }

        return query;
    }

    #endregion IOrganizingAuthorityService Implementation

    #region Private Methods
    /// <summary>
    /// Applies access control filtering based on the current user's permissions.
    /// </summary>
    private async Task<IQueryable<OrganizingAuthority>> ApplyAccessControlFilteringAsync(
        IQueryable<OrganizingAuthority> query
    )
    {
        var currentUserId = _httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
        var isAuthenticated = !string.IsNullOrEmpty(currentUserId);

        if (!isAuthenticated)
        {
            return query.Where(oa => !oa.Private && oa.Approved);
        }

        var isHmfic =
            _httpContextAccessor.HttpContext?.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true")
            ?? false;
        if (isHmfic)
        {
            return query;
        }

        var authorizedOaIds = await _context
            .UserAuthActions.Where(uaa =>
                uaa.UserId == currentUserId
                && (
                    uaa.AuthActionName == AuthTypes.Actions.VIEW
                    || uaa.AuthActionName == AuthTypes.Actions.EDIT
                    || uaa.AuthActionName == AuthTypes.Actions.ADMIN
                )
            )
            .Select(uaa => uaa.TargetId)
            .ToListAsync();

        return query.Where(oa =>
            (!oa.Private && oa.Approved) || authorizedOaIds.Contains(oa.Id!) || oa.CreatedById == currentUserId
        );
    }
    #endregion Private Methods
}
