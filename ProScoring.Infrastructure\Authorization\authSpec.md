# Authorization Model Specification for Regatta Scoring Application

## 1. Overview
This authorization model governs access control for **organizing authorities, regattas, and races**, supporting:
- **Hierarchical permissions** (Organizing Authorities → Regattas → Races)
- **Role-based and direct assignments**
- **Explicit permission overrides**
- **Configurable inheritance rules**
- **Database-driven authorization logic**
- **Granular entity-type permissions for creation actions**
- **Application-wide permissions using `UNIVERSAL_TARGET = "*"`**

---

## 2. Core Authorization Actions

### Standard Actions
- `ADMIN` – Full system access, including managing users.
- `DELETE` – Remove an entity.
- `EDIT` – Modify entity details.
- `VIEW` – Read entity information.
- `CREATE` – Create new instances of entity types (e.g., `RaceResults`, `Participant`).

### Entity-Type Permissions
Some actions apply **to entity types** rather than specific instances.
- Example: `"User X can CREATE RaceResults under Regatta123"`
- Example: `"User Y can EDIT all RaceResults under Regatta456"`

### Hierarchical Inheritance
- Users may inherit permissions from an **organizing authority** down to regattas and races.
- Overrides allow explicit changes at the **regatta or race level**.

### Application-Wide Rules
- Some permissions apply **globally** across the entire application.
- `"*"` is treated as an authorization target to signify **application-wide access**.
- Example: `"User X can MANAGE_USERS on '*'"` → Grants global user management rights.

### Application to Regattas

The standard actions apply to Regatta entities as follows, where `EntityType` is 'Regatta':

*   **`CREATE` (on `EntityType` 'Regatta'):** Allows a user to create a new regatta event.
    *   This action is typically granted to users associated with an Organizing Authority they have rights to manage.
    *   The `RegattaController` (`ProScoring.Blazor/Controllers/RegattaController.cs`) handles regatta creation. This controller is decorated with `[Authorize]`, requiring authentication for all its actions. The service layer (`RegattaService`) may perform further authorization (e.g., verifying rights to the specified Organizing Authority) and can throw `UnauthorizedAccessException`.
*   **`VIEW` (on `EntityType` 'Regatta'):** Allows a user to view the details of a specific regatta.
    *   Access may vary based on the regatta's `Private` property. Public regattas might be viewable more broadly, while private regattas would require specific permissions (e.g., membership in the organizing OA, direct assignment, or an `ADMIN` role on the OA).
    *   The `RegattaController`'s `GetById` method, also protected by `[Authorize]`, handles fetching regatta details.
*   **`EDIT` (on `EntityType` 'Regatta'):** Allows a user to modify an existing regatta's details. This includes changes to dates, descriptions, fees, participant lists, race schedules, and other settings.
    *   This permission is typically granted to users who manage the regatta, such as designated officials of the Organizing Authority or those with `ADMIN` rights on the Regatta itself.
    *   Future `Update` methods in the `RegattaController` will handle these modifications.
*   **`DELETE` (on `EntityType` 'Regatta'):** Allows a user to delete a regatta.
    *   This is a destructive action and should be granted cautiously, typically to users with `ADMIN` rights on the Regatta or its parent Organizing Authority.
    *   Future `Delete` methods in the `RegattaController` will handle this.

When recording permissions for these actions in the `UserAuthActions` table:
*   `ActionName` would be one of the standard actions (`CREATE`, `VIEW`, `EDIT`, `DELETE`).
*   `EntityType` would be 'Regatta' (or a constant representing it).
*   `TargetId` would be the specific Regatta's unique ID (e.g., "G12345"). For `CREATE`, `TargetId` might be the parent Organizing Authority's ID if the permission is to create regattas under that OA, or "*" if it's a global create regatta permission (less common).

### Application to Organizing Authorities

Similarly, standard actions apply to Organizing Authority (OA) entities, where `EntityType` is 'OrganizingAuthority':

*   **`CREATE` (on `EntityType` 'OrganizingAuthority'):** Allows a user to create a new Organizing Authority.
    *   The `OrganizingAuthorityController` handles OA creation. Its `Create` method also grants the creator `ADMIN` rights over the newly created OA.
    *   The Blazor page `OrganizingAuthorityPages/Create.razor` facilitates this.
    *   The `OrganizingAuthorityController` itself (as last observed) is not globally protected by `[Authorize]`. Thus, for direct API calls, protection relies on service-layer checks or method-level attributes. However, Blazor pages initiating these calls typically have their own authorization.
*   **`VIEW` (on `EntityType` 'OrganizingAuthority'):** Allows a user to view details of an Organizing Authority.
    *   Visibility might depend on the OA's `Private` status or user roles. Public OAs might be listed more broadly (e.g., in `OrganizingAuthorityPages/Index.razor` or the shared `OrganizingAuthoritiesList.razor`).
    *   Details are displayed on `OrganizingAuthorityPages/Details.razor`.
*   **`EDIT` (on `EntityType` 'OrganizingAuthority'):** Allows a user to modify an existing Organizing Authority's details. This includes changing its name, contact information, address, and its `Approved` status.
    *   Modifying the `Approved` status is a critical aspect of this `EDIT` action and is typically restricted to users with high-level administrative roles (e.g., "HMFIC" role checking this permission).
    *   The Blazor page `OrganizingAuthorityPages/Edit.razor` is protected by the `EditAuthorizationForPageWithIdHandler.PolicyName` policy, which should resolve to the `EDIT` action on the specific OA.
    *   Managing user memberships or assigning roles within an OA would also typically fall under `EDIT` or `ADMIN` permissions on the OA.
*   **`DELETE` (on `EntityType` 'OrganizingAuthority'):** Allows a user to delete an Organizing Authority.
    *   The Blazor page `OrganizingAuthorityPages/Delete.razor` is protected by the `DeleteAuthorizationForPageWithIdHandler.PolicyName` policy, resolving to the `DELETE` action on the specific OA.

When recording permissions for these actions in the `UserAuthActions` table:
*   `ActionName` would be one of the standard actions.
*   `EntityType` would be 'OrganizingAuthority' (or a constant representing it).
*   `TargetId` would be the specific OA's unique ID (e.g., "O_abc123"). For `CREATE` on an OA (if such a permission exists globally, which is rare), `TargetId` might be "*".

Hierarchical permissions are significant here: permissions granted at the Organizing Authority level (e.g., `ADMIN` or `EDIT` on an OA) can implicitly grant corresponding permissions over associated Regattas and Races, as defined by the inheritance rules.

---

## 3. Role-Based vs Direct Permission Assignment

### Roles
- Users inherit permissions via predefined **roles** (e.g., `Regatta Manager`, `Scorekeeper`).
- Roles can apply at **the organizing authority** or **individual regatta level**.

### Direct Assignments
- Users can be granted **specific permissions** at **specific regatta/race levels**.
- Direct assignments **override** role-based permissions when necessary.

### Overrides Handling
- Overrides persist **even if role assignments change**.
- They allow explicit **permission grants** or **denials** per entity.

---

## 4. Data Storage Structure

### Database Tables

#### AuthActions
Stores all available authorization actions.

| Column | Type | Description |
|--------|------|------------|
| `ActionName` | `string` | Name of the action (e.g., `EDIT`, `CREATE`). |

#### ActionHierarchy
Defines parent-child relationships between actions.

| Column | Type | Description |
|--------|------|------------|
| `ParentActionName` | `string` | Parent action granting inference. |
| `ChildActionName` | `string` | Child action inferred from parent. |

#### UserAuthActions
Defines assigned actions for users.

| Column | Type | Description |
|--------|------|------------|
| `UserId` | `string` | Associated user. |
| `TargetId` | `string` | ID of the entity (`Regatta123`, `"*"` for global rules). |
| `EntityType` | `string` | Type of entity affected (e.g., `"RaceResult"`). |
| `ActionName` | `string` | Granted action (`CREATE`, `EDIT`). |

#### OverridePermissions
Tracks explicit permission modifications. Now inherits from `LastChangeTrackingWithAutoInsertedIdBase` for consistent ID generation and change tracking.

| Column | Type | Description |
|--------|------|------------|
| `Id` | `string` | Unique entry ID with 'OP' prefix (auto-generated). |
| `UserId` | `string` | Associated user. |
| `TargetId` | `string` | ID of the entity (regatta/race). |
| `ActionName` | `string` | Modified action. |
| `ScopedEntityType` | `string?` | Optional entity type for scoped permissions. |
| `IsAllowed` | `bool` | Whether the action is explicitly allowed or denied. |
| `Reason` | `string?` | Optional reason for the override. |
| `ExpiresAt` | `DateTimeOffset?` | Optional expiration date/time. |
| `CreatedAt` | `DateTimeOffset` | When the override was created (inherited). |
| `CreatedById` | `string?` | Who created the override (inherited). |
| `UpdatedAt` | `DateTimeOffset` | When the override was last updated (inherited). |
| `UpdatedById` | `string?` | Who last updated the override (inherited). |

---

## 5. Authorization Mechanism

### Authorization Check Method
```cs
Task<bool> IsAuthorizedAsync(ClaimsPrincipal actor, string targetId, string action);
